import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Grid,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  Stack,
} from '@mui/material';
import {
  Add as AddIcon,
  Security as SecurityIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
} from '@mui/icons-material';
import { PolicyParser } from '@/lib/policyParser';
import { OSO_COLORS } from '@/styles/osoTheme';
import OpaCodeSidebar from './OpaCodeSidebar';
import PermissionCard from './PermissionCard';
import PermissionBuilder from './PermissionBuilder';

interface TaskTypesStepProps {
  data: {
    taskTypes: Record<string, any>;
    packageName: string;
    globalSettings: {
      addConditionButton: boolean;
      allowUserTypeCheck: boolean;
      allowedUserTypes: string[];
    };
  };
  onChange: (updates: any) => void;
}

interface TaskTypeDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (taskTypeName: string) => void;
  existingNames: string[];
}

interface StatusDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (statusName: string) => void;
  existingStatuses: string[];
}



const TaskTypeDialog: React.FC<TaskTypeDialogProps> = ({
  open,
  onClose,
  onSave,
  existingNames,
}) => {
  const [taskTypeName, setTaskTypeName] = useState('');
  const [customTaskTypeName, setCustomTaskTypeName] = useState('');
  const [error, setError] = useState('');

  const predefinedTaskTypes = [
    'mfl-lap-sanction',
    'document-approval',
    'payment-authorization',
    'user-registration',
    'content-moderation',
    'workflow-task',
  ];

  // Reset dialog state when opened/closed
  useEffect(() => {
    if (!open) {
      setTaskTypeName('');
      setCustomTaskTypeName('');
      setError('');
    }
  }, [open]);

  const handleSave = () => {
    try {
      console.log('TaskTypeDialog handleSave called');
      console.log('taskTypeName:', taskTypeName);
      console.log('customTaskTypeName:', customTaskTypeName);

      const finalTaskTypeName = taskTypeName === 'custom' ? customTaskTypeName.trim() : taskTypeName.trim();
      console.log('finalTaskTypeName:', finalTaskTypeName);

      if (!finalTaskTypeName) {
        setError('Task type name is required');
        return;
      }
      if (existingNames.includes(finalTaskTypeName)) {
        setError('Task type name already exists');
        return;
      }

      // Clear any previous errors
      setError('');

      console.log('Calling onSave with:', finalTaskTypeName);
      console.log('onSave function:', onSave);

      // Call the onSave function - parent will handle success/failure
      if (typeof onSave === 'function') {
        onSave(finalTaskTypeName);
      } else {
        console.error('onSave is not a function:', onSave);
        setError('Internal error: onSave is not a function');
      }
    } catch (error) {
      console.error('Error in TaskTypeDialog handleSave:', error);
      setError('An unexpected error occurred. Please try again.');
    }
  };

  const handleClose = () => {
    setError(''); // Clear any errors when closing
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>Add Task Type</DialogTitle>
      <DialogContent>
        <Typography variant="body2" color="textSecondary" paragraph>
          Task types define the different kinds of operations or workflows in your system.
        </Typography>
        
        <FormControl fullWidth margin="normal">
          <InputLabel>Select Predefined Task Type</InputLabel>
          <Select
            value={taskTypeName}
            onChange={(e) => setTaskTypeName(e.target.value)}
            label="Select Predefined Task Type"
          >
            {predefinedTaskTypes.map((type) => (
              <MenuItem key={type} value={type}>
                {type}
              </MenuItem>
            ))}
            <MenuItem value="custom">Custom Task Type</MenuItem>
          </Select>
        </FormControl>

        {taskTypeName === 'custom' && (
          <TextField
            autoFocus
            fullWidth
            margin="normal"
            label="Custom Task Type Name"
            value={customTaskTypeName}
            onChange={(e) => setCustomTaskTypeName(e.target.value)}
            placeholder="e.g., my-custom-task"
            error={!!error}
            helperText={error || 'Use lowercase letters, numbers, and hyphens'}
          />
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Cancel</Button>
        <Button onClick={handleSave} variant="contained">
          Add Task Type
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const StatusDialog: React.FC<StatusDialogProps> = ({
  open,
  onClose,
  onSave,
  existingStatuses,
}) => {
  const [statusName, setStatusName] = useState('');
  const [error, setError] = useState('');

  const predefinedStatuses = [
    'APPROVED',
    'UNSTARTED',
    'PENDING',
    'REJECTED',
    'COMPLETED',
    'IN_PROGRESS',
    'CANCELLED',
    'DRAFT',
    'SUBMITTED',
    'UNDER_REVIEW',
  ];

  // Reset dialog state when opened/closed
  useEffect(() => {
    if (!open) {
      setStatusName('');
      setError('');
    }
  }, [open]);

  const handleSave = () => {
    if (!statusName.trim()) {
      setError('Status name is required');
      return;
    }
    if (existingStatuses.includes(statusName)) {
      setError('Status already exists');
      return;
    }

    try {
      onSave(statusName);
      // State will be reset by useEffect when dialog closes
      onClose();
    } catch (error) {
      console.error('Error saving status:', error);
      setError('Failed to save status. Please try again.');
    }
  };

  const handleClose = () => {
    setError(''); // Clear any errors when closing
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>Add Status</DialogTitle>
      <DialogContent>
        <Typography variant="body2" color="textSecondary" paragraph>
          Statuses represent the different states a task can be in.
        </Typography>
        
        <FormControl fullWidth margin="normal">
          <InputLabel>Select Status</InputLabel>
          <Select
            value={statusName}
            onChange={(e) => setStatusName(e.target.value)}
            label="Select Status"
          >
            {predefinedStatuses.map((status) => (
              <MenuItem key={status} value={status}>
                {status}
              </MenuItem>
            ))}
            <MenuItem value="custom">Custom Status</MenuItem>
          </Select>
        </FormControl>

        {(statusName === 'custom' || !predefinedStatuses.includes(statusName)) && (
          <TextField
            fullWidth
            margin="normal"
            label="Custom Status Name"
            value={statusName === 'custom' ? '' : statusName}
            onChange={(e) => setStatusName(e.target.value)}
            placeholder="e.g., CUSTOM_STATUS"
            error={!!error}
            helperText={error || 'Use UPPERCASE letters and underscores'}
          />
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Cancel</Button>
        <Button onClick={handleSave} variant="contained">
          Add Status
        </Button>
      </DialogActions>
    </Dialog>
  );
};



// Extracted content component without sidebar for use in ConsolidatedSetupStep
export const TaskTypesContent: React.FC<TaskTypesStepProps> = ({ data, onChange }) => {
  // Ensure data has the expected structure
  const safeData = {
    taskTypes: data?.taskTypes || {},
    packageName: data?.packageName || '',
    globalSettings: data?.globalSettings || {
      addConditionButton: false,
      allowUserTypeCheck: false,
      allowedUserTypes: ['admin', 'user'],
    },
  };

  const [taskTypeDialogOpen, setTaskTypeDialogOpen] = useState(false);
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [permissionBuilderOpen, setPermissionBuilderOpen] = useState(false);
  const [selectedTaskType, setSelectedTaskType] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [editingButton, setEditingButton] = useState<{
    name: string;
    condition: any;
  } | null>(null);

  // TaskTypesContent handlers
  const handleContentAddTaskType = (taskTypeName: string) => {
    if (!taskTypeName || typeof taskTypeName !== 'string') {
      console.error('Invalid task type name:', taskTypeName);
      return false;
    }

    const currentTaskTypes = safeData?.taskTypes || {};

    // Check if task type already exists
    if (currentTaskTypes[taskTypeName]) {
      console.warn('Task type already exists:', taskTypeName);
      return false;
    }

    try {
      const newTaskType = PolicyParser.createEmptyTaskType();

      if (!newTaskType) {
        console.error('Failed to create empty task type');
        return false;
      }

      const updatedTaskTypes = {
        ...currentTaskTypes,
        [taskTypeName]: newTaskType,
      };

      if (typeof onChange !== 'function') {
        console.error('onChange is not a function:', onChange);
        return false;
      }

      onChange({ taskTypes: updatedTaskTypes });
      return true;
    } catch (error) {
      console.error('Error adding task type:', error);
      return false;
    }
  };

  const handleContentEditTaskType = (taskTypeName: string) => {
    console.log('Edit task type:', taskTypeName);
  };

  const handleContentDeleteTaskType = (taskTypeName: string) => {
    try {
      const currentTaskTypes = safeData.taskTypes;
      const updatedTaskTypes = { ...currentTaskTypes };
      delete updatedTaskTypes[taskTypeName];
      onChange({ taskTypes: updatedTaskTypes });
    } catch (error) {
      console.error('Error deleting task type:', error);
    }
  };

  const handleContentAddStatus = (taskTypeName: string) => {
    setSelectedTaskType(taskTypeName);
    setStatusDialogOpen(true);
  };

  const handleContentAddStatusSave = (statusName: string) => {
    try {
      if (selectedTaskType) {
        const currentTaskTypes = safeData.taskTypes;
        const updatedTaskTypes = { ...currentTaskTypes };
        const taskType = { ...updatedTaskTypes[selectedTaskType] };

        if (!taskType.status) {
          taskType.status = {};
        }

        taskType.status[statusName] = {
          buttons: {},
        };

        updatedTaskTypes[selectedTaskType] = taskType;
        onChange({ taskTypes: updatedTaskTypes });
      }
      setStatusDialogOpen(false);
      setSelectedTaskType('');
    } catch (error) {
      console.error('Error adding status:', error);
      setStatusDialogOpen(false);
      setSelectedTaskType('');
    }
  };

  const handleContentEditStatus = (taskTypeName: string, statusName: string) => {
    console.log('Edit status:', taskTypeName, statusName);
  };

  const handleContentDeleteStatus = (taskTypeName: string, statusName: string) => {
    const updatedTaskTypes = { ...safeData.taskTypes };
    const taskType = { ...updatedTaskTypes[taskTypeName] };

    if (taskType.status) {
      delete taskType.status[statusName];
    }

    updatedTaskTypes[taskTypeName] = taskType;
    onChange({ taskTypes: updatedTaskTypes });
  };

  const handleContentAddButton = (taskTypeName: string, statusName: string) => {
    setSelectedTaskType(taskTypeName);
    setSelectedStatus(statusName);
    setEditingButton(null);
    setPermissionBuilderOpen(true);
  };

  const handleContentEditButton = (taskTypeName: string, statusName: string, buttonName: string) => {
    const button = safeData.taskTypes[taskTypeName]?.status?.[statusName]?.buttons?.[buttonName];
    if (button) {
      setSelectedTaskType(taskTypeName);
      setSelectedStatus(statusName);
      setEditingButton({
        name: buttonName,
        condition: button.condition,
      });
      setPermissionBuilderOpen(true);
    }
  };

  const handleContentDeleteButton = (taskTypeName: string, statusName: string, buttonName: string) => {
    const updatedTaskTypes = { ...safeData.taskTypes };
    const taskType = { ...updatedTaskTypes[taskTypeName] };

    if (taskType.status && taskType.status[statusName] && taskType.status[statusName].buttons) {
      delete taskType.status[statusName].buttons[buttonName];
      if (Object.keys(taskType.status[statusName].buttons).length === 0) {
        taskType.status[statusName].buttons = {};
      }
    }
    updatedTaskTypes[taskTypeName] = taskType;
    onChange({ taskTypes: updatedTaskTypes });
  };

  const handleContentPermissionSave = (buttonName: string, condition: any) => {
    if (selectedTaskType && selectedStatus) {
      const updatedTaskTypes = { ...safeData.taskTypes };
      const taskType = { ...updatedTaskTypes[selectedTaskType] };

      if (!taskType.status) {
        taskType.status = {};
      }
      if (!taskType.status[selectedStatus]) {
        taskType.status[selectedStatus] = { buttons: {} };
      }
      if (!taskType.status[selectedStatus].buttons) {
        taskType.status[selectedStatus].buttons = {};
      }

      taskType.status[selectedStatus].buttons[buttonName] = {
        condition: condition,
      };

      updatedTaskTypes[selectedTaskType] = taskType;
      onChange({ taskTypes: updatedTaskTypes });
    }
    setPermissionBuilderOpen(false);
    setSelectedTaskType('');
    setSelectedStatus('');
    setEditingButton(null);
  };







  return (
    <Box sx={{ minHeight: 'fit-content' }}>
      {Object.keys(safeData.taskTypes).length === 0 ? (
        <Box
          sx={{
            p: 2.5,
            textAlign: 'center',
            border: '1px dashed #d1d9e0',
            borderRadius: '6px',
            bgcolor: '#f6f8fa',
          }}
        >
          <SecurityIcon sx={{ fontSize: 24, color: '#656d76', mb: 1 }} />
          <Typography variant="body2" sx={{ color: '#24292f', fontWeight: 500, mb: 0.5 }}>
            No Task Types Yet
          </Typography>
          <Typography variant="caption" sx={{ color: '#656d76', mb: 1.5, display: 'block' }}>
            Add a task type to get started
          </Typography>
          <Button
            variant="outlined"
            size="small"
            startIcon={<AddIcon />}
            onClick={() => setTaskTypeDialogOpen(true)}
            sx={{
              borderColor: '#d1d9e0',
              color: '#24292f',
              textTransform: 'none',
              fontSize: '0.75rem',
              px: 2,
              py: 0.5,
              '&:hover': { borderColor: '#0969da', backgroundColor: '#f6f8fa' },
            }}
          >
            Add Task Type
          </Button>
        </Box>
      ) : (
        <Box>
          {/* Compact Header */}
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={1.5}>
            <Box display="flex" alignItems="center" gap={1}>
              <Typography variant="body2" sx={{ fontWeight: 600, color: '#24292f', fontSize: '0.875rem' }}>
                Task Types
              </Typography>
              <Chip
                label={Object.keys(safeData.taskTypes).length}
                size="small"
                sx={{
                  height: 18,
                  fontSize: '0.7rem',
                  backgroundColor: '#e7f3ff',
                  color: '#0969da',
                }}
              />
            </Box>
            <Button
              variant="text"
              startIcon={<AddIcon />}
              onClick={() => setTaskTypeDialogOpen(true)}
              size="small"
              sx={{
                textTransform: 'none',
                fontSize: '0.75rem',
                color: '#656d76',
                minWidth: 'auto',
                px: 1,
                '&:hover': { color: '#0969da', bgcolor: 'transparent' },
              }}
            >
              Add
            </Button>
          </Box>

          {/* Compact Task Types List */}
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
            {Object.entries(safeData.taskTypes).map(([taskTypeName, taskType]: [string, any]) => (
              <PermissionCard
                key={taskTypeName}
                taskTypeName={taskTypeName}
                taskType={taskType}
                onEditTaskType={handleContentEditTaskType}
                onDeleteTaskType={handleContentDeleteTaskType}
                onAddStatus={handleContentAddStatus}
                onEditStatus={handleContentEditStatus}
                onDeleteStatus={handleContentDeleteStatus}
                onAddButton={handleContentAddButton}
                onEditButton={handleContentEditButton}
                onDeleteButton={handleContentDeleteButton}
              />
            ))}
          </Box>
        </Box>
      )}

      {/* Dialogs */}
      <TaskTypeDialog
        open={taskTypeDialogOpen}
        onClose={() => setTaskTypeDialogOpen(false)}
        onSave={(taskTypeName) => {
          try {
            console.log('Dialog onSave called with:', taskTypeName);
            const success = handleContentAddTaskType(taskTypeName);
            console.log('handleContentAddTaskType returned:', success);
            if (success) {
              setTaskTypeDialogOpen(false);
            }
            // If not successful, dialog stays open for user to retry
          } catch (error) {
            console.error('Error in dialog onSave:', error);
          }
        }}
        existingNames={Object.keys(safeData?.taskTypes || {})}
      />

      <StatusDialog
        open={statusDialogOpen}
        onClose={() => setStatusDialogOpen(false)}
        onSave={handleContentAddStatusSave}
        existingStatuses={
          selectedTaskType ? Object.keys(safeData.taskTypes[selectedTaskType]?.status || {}) : []
        }
      />

      <PermissionBuilder
        open={permissionBuilderOpen}
        onClose={() => setPermissionBuilderOpen(false)}
        onSave={handleContentPermissionSave}
        existingButtons={
          selectedTaskType && selectedStatus
            ? Object.keys(safeData.taskTypes[selectedTaskType]?.status?.[selectedStatus]?.buttons || {})
            : []
        }
        title={editingButton ? 'Edit Permission' : 'Add Permission'}
        initialButton={editingButton || undefined}
      />
    </Box>
  );
};

const TaskTypesStep: React.FC<TaskTypesStepProps> = ({ data, onChange }) => {
  const [taskTypeDialogOpen, setTaskTypeDialogOpen] = useState(false);
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [permissionBuilderOpen, setPermissionBuilderOpen] = useState(false);
  const [selectedTaskType, setSelectedTaskType] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [editingButton, setEditingButton] = useState<{
    name: string;
    condition: any;
  } | null>(null);
  const [sidebarVisible, setSidebarVisible] = useState(true);

  const handleAddTaskType = (taskTypeName: string) => {
    const newTaskTypes = {
      ...(data.taskTypes || {}),
      [taskTypeName]: PolicyParser.createEmptyTaskType(),
    };
    onChange({ taskTypes: newTaskTypes });
  };

  const handleEditTaskType = (taskTypeName: string) => {
    // For now, just focus on the task type - could add rename functionality later
    console.log('Edit task type:', taskTypeName);
  };

  const handleDeleteTaskType = (taskTypeName: string) => {
    const newTaskTypes = { ...(data.taskTypes || {}) };
    delete newTaskTypes[taskTypeName];
    onChange({ taskTypes: newTaskTypes });
  };

  const handleAddStatus = (taskTypeName: string) => {
    setSelectedTaskType(taskTypeName);
    setStatusDialogOpen(true);
  };

  const handleEditStatus = (taskTypeName: string, statusName: string) => {
    // Could add status editing functionality
    console.log('Edit status:', taskTypeName, statusName);
  };

  const handleDeleteStatus = (taskTypeName: string, statusName: string) => {
    const newTaskTypes = { ...(data.taskTypes || {}) };
    if (newTaskTypes[taskTypeName]?.status) {
      delete newTaskTypes[taskTypeName].status[statusName];
    }
    onChange({ taskTypes: newTaskTypes });
  };

  const handleAddStatusSave = (statusName: string) => {
    const currentTaskTypes = data.taskTypes || {};
    const newTaskTypes = {
      ...currentTaskTypes,
      [selectedTaskType]: {
        ...(currentTaskTypes[selectedTaskType] || {}),
        status: {
          ...(currentTaskTypes[selectedTaskType]?.status || {}),
          [statusName]: { buttons: {} },
        },
      },
    };
    onChange({ taskTypes: newTaskTypes });
  };

  const handleAddButton = (taskTypeName: string, statusName: string) => {
    setSelectedTaskType(taskTypeName);
    setSelectedStatus(statusName);
    setEditingButton(null);
    setPermissionBuilderOpen(true);
  };

  const handleEditButton = (taskTypeName: string, statusName: string, buttonName: string) => {
    const button = (data.taskTypes || {})[taskTypeName]?.status?.[statusName]?.buttons?.[buttonName];
    if (button) {
      setSelectedTaskType(taskTypeName);
      setSelectedStatus(statusName);
      setEditingButton({
        name: buttonName,
        condition: button.condition,
      });
      setPermissionBuilderOpen(true);
    }
  };

  const handleDeleteButton = (taskTypeName: string, statusName: string, buttonName: string) => {
    const newTaskTypes = { ...(data.taskTypes || {}) };
    if (newTaskTypes[taskTypeName]?.status?.[statusName]?.buttons) {
      delete newTaskTypes[taskTypeName].status[statusName].buttons[buttonName];
    }
    onChange({ taskTypes: newTaskTypes });
  };

  const handlePermissionSave = (buttonName: string, condition: any) => {
    if (!selectedTaskType || !selectedStatus) return;

    const newTaskTypes = { ...(data.taskTypes || {}) };

    // Ensure the structure exists
    if (!newTaskTypes[selectedTaskType]) {
      newTaskTypes[selectedTaskType] = { status: {} };
    }
    if (!newTaskTypes[selectedTaskType].status) {
      newTaskTypes[selectedTaskType].status = {};
    }
    if (!newTaskTypes[selectedTaskType].status[selectedStatus]) {
      newTaskTypes[selectedTaskType].status[selectedStatus] = { buttons: {} };
    }
    if (!newTaskTypes[selectedTaskType].status[selectedStatus].buttons) {
      newTaskTypes[selectedTaskType].status[selectedStatus].buttons = {};
    }

    // If editing, remove the old button first
    if (editingButton && editingButton.name !== buttonName) {
      delete newTaskTypes[selectedTaskType].status[selectedStatus].buttons[editingButton.name];
    }

    newTaskTypes[selectedTaskType].status[selectedStatus].buttons[buttonName] = {
      condition,
    };

    onChange({ taskTypes: newTaskTypes });
  };



  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Split View Container */}
      <Box sx={{
        flexGrow: 1,
        display: 'flex',
        gap: 1,
        overflow: 'hidden'
      }}>
        {/* Main Content Panel */}
        <Box sx={{
          flex: sidebarVisible ? '1 1 50%' : '1 1 100%',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          transition: 'flex 0.3s ease-in-out',
        }}>
        {/* Action Bar */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-end',
            mb: 2,
          }}
        >


          <Stack direction="row" spacing={1}>
            <Tooltip title="Toggle OPA Code Preview">
              <Button
                variant="outlined"
                size="small"
                startIcon={sidebarVisible ? <VisibilityOffIcon /> : <VisibilityIcon />}
                onClick={() => setSidebarVisible(!sidebarVisible)}
                sx={{
                  textTransform: 'none',
                  fontSize: '0.75rem',
                  px: 1.5,
                  py: 0.5,
                  minWidth: 'auto',
                }}
              >
                {sidebarVisible ? 'Hide' : 'Show'} Code
              </Button>
            </Tooltip>



            <Button
              variant="contained"
              size="small"
              startIcon={<AddIcon />}
              onClick={() => setTaskTypeDialogOpen(true)}
              sx={{
                bgcolor: OSO_COLORS.primary,
                textTransform: 'none',
                fontSize: '0.75rem',
                px: 1.5,
                py: 0.5,
                minWidth: 'auto',
              }}
            >
              Add Task Type
            </Button>
          </Stack>
        </Box>

        {/* Compact Main Content */}
        <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
          {Object.keys(data.taskTypes || {}).length === 0 ? (
            <Box
              sx={{
                p: 3,
                textAlign: 'center',
                border: `1px dashed ${OSO_COLORS.border}`,
                borderRadius: 1,
                bgcolor: OSO_COLORS.surface,
              }}
            >
              <SecurityIcon
                sx={{
                  fontSize: 32,
                  color: OSO_COLORS.primary,
                  mb: 1,
                }}
              />
              <Typography
                variant="h6"
                sx={{
                  color: OSO_COLORS.textPrimary,
                  fontWeight: 600,
                  mb: 0.5,
                  fontSize: '1rem',
                }}
              >
                No Task Types Yet
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: OSO_COLORS.textSecondary,
                  mb: 2,
                  fontSize: '0.85rem',
                }}
              >
                Add a task type to get started
              </Typography>

              <Button
                variant="contained"
                size="small"
                startIcon={<AddIcon />}
                onClick={() => setTaskTypeDialogOpen(true)}
                sx={{
                  bgcolor: OSO_COLORS.primary,
                  textTransform: 'none',
                  fontSize: '0.8rem',
                  px: 2,
                  py: 0.5,
                }}
              >
                Add Task Type
              </Button>
            </Box>
          ) : (
            <Box>
              {/* Task Types Summary */}
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 600,
                    color: OSO_COLORS.textPrimary,
                    fontSize: '1.1rem',
                  }}
                >
                  Configured Task Types ({Object.keys(data.taskTypes || {}).length})
                </Typography>

                <Button
                  variant="text"
                  startIcon={<AddIcon />}
                  onClick={() => setTaskTypeDialogOpen(true)}
                  size="small"
                  sx={{
                    textTransform: 'none',
                    fontSize: '0.8rem',
                    color: OSO_COLORS.textSecondary,
                    '&:hover': {
                      color: OSO_COLORS.primary,
                      bgcolor: 'transparent',
                    },
                  }}
                >
                  Add Task Type
                </Button>
              </Box>

              {/* Enhanced Task Types Grid */}
              <Grid container spacing={3}>
                {Object.entries(data.taskTypes || {}).map(([taskTypeName, taskType]: [string, any]) => (
                  <Grid item xs={12} xl={6} key={taskTypeName}>
                    <PermissionCard
                      taskTypeName={taskTypeName}
                      taskType={taskType}
                      onEditTaskType={handleEditTaskType}
                      onDeleteTaskType={handleDeleteTaskType}
                      onAddStatus={handleAddStatus}
                      onEditStatus={handleEditStatus}
                      onDeleteStatus={handleDeleteStatus}
                      onAddButton={handleAddButton}
                  onEditButton={handleEditButton}
                  onDeleteButton={handleDeleteButton}
                />
              </Grid>
            ))}
          </Grid>
            </Box>
          )}
        </Box>
        </Box>

        {/* OPA Code Panel - Inline Split View */}
        {sidebarVisible && (
          <Box sx={{
            flex: '1 1 50%',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
            borderLeft: `1px solid ${OSO_COLORS.border}`,
            bgcolor: OSO_COLORS.surface,
          }}>
            <OpaCodeSidebar
              taskTypes={data.taskTypes || {}}
              globalSettings={data.globalSettings}
              packageName={data.packageName}
              isVisible={true}
              onToggleVisibility={() => setSidebarVisible(!sidebarVisible)}
            />
          </Box>
        )}
      </Box>

      {/* Dialogs */}
      <TaskTypeDialog
          open={taskTypeDialogOpen}
          onClose={() => setTaskTypeDialogOpen(false)}
          onSave={handleAddTaskType}
          existingNames={Object.keys(data.taskTypes || {})}
        />

      <StatusDialog
          open={statusDialogOpen}
          onClose={() => setStatusDialogOpen(false)}
          onSave={handleAddStatusSave}
          existingStatuses={
            selectedTaskType ? Object.keys((data.taskTypes || {})[selectedTaskType]?.status || {}) : []
          }
        />

      <PermissionBuilder
          open={permissionBuilderOpen}
          onClose={() => setPermissionBuilderOpen(false)}
          onSave={handlePermissionSave}
          existingButtons={
            selectedTaskType && selectedStatus
              ? Object.keys((data.taskTypes || {})[selectedTaskType]?.status?.[selectedStatus]?.buttons || {})
              : []
          }
          title={editingButton ? 'Edit Permission' : 'Add Permission'}
          initialButton={editingButton || undefined}
        />
    </Box>
  );
};

export default TaskTypesStep;
