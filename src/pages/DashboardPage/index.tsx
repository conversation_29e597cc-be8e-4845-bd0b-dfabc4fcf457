import React from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  Chip,
  Avatar,
  Stack,
  LinearProgress,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Inventory as ProductsIcon,
  Policy as PoliciesIcon,
  History as DecisionsIcon,
  TrendingUp as TrendingUpIcon,
  Dashboard as DashboardIcon,
  AccountBalance as BankIcon,
  Security as SecurityIcon,
  DeviceHub as DeviceIcon,
  Api as ApiIcon,
  Circle as CircleIcon,
  Refresh as RefreshIcon,
  MoreVert as MoreIcon,
} from '@mui/icons-material';

// Financial Products Mock Data
const mockMetrics = {
  totalProducts: 6,
  totalPolicies: 54,
  totalDecisions: 2847,
  productsHealth: {
    healthy: 5,
    warning: 1,
    critical: 0,
  },
  policyDistribution: {
    active: 45,
    draft: 7,
    deprecated: 2,
  },
};

const mockProducts = [
  {
    id: '1',
    name: 'Lender Dashboard',
    type: 'web',
    status: 'active',
    policies: 8,
    decisions24h: 456,
    uptime: 99.9,
    icon: <DashboardIcon />,
    color: '#1f883d',
  },
  {
    id: '2',
    name: 'Platform Dashboard',
    type: 'web',
    status: 'active',
    policies: 12,
    decisions24h: 789,
    uptime: 99.8,
    icon: <DashboardIcon />,
    color: '#0969da',
  },
  {
    id: '3',
    name: 'Lisa',
    type: 'api',
    status: 'active',
    policies: 6,
    decisions24h: 1234,
    uptime: 99.95,
    icon: <SecurityIcon />,
    color: '#8250df',
  },
  {
    id: '4',
    name: 'Sentinel',
    type: 'service',
    status: 'active',
    policies: 15,
    decisions24h: 234,
    uptime: 99.7,
    icon: <SecurityIcon />,
    color: '#da3633',
  },
  {
    id: '5',
    name: 'BankConnect',
    type: 'api',
    status: 'maintenance',
    policies: 9,
    decisions24h: 89,
    uptime: 98.5,
    icon: <BankIcon />,
    color: '#fb8500',
  },
  {
    id: '6',
    name: 'DeviceConnect',
    type: 'service',
    status: 'active',
    policies: 4,
    decisions24h: 67,
    uptime: 99.6,
    icon: <DeviceIcon />,
    color: '#1f883d',
  },
];

const recentActivity = [
  {
    id: '1',
    type: 'policy_deployed',
    message: 'New lending policy deployed to Lender Dashboard',
    timestamp: '2 minutes ago',
    product: 'Lender Dashboard',
  },
  {
    id: '2',
    type: 'security_alert',
    message: 'Sentinel detected suspicious activity - auto-blocked',
    timestamp: '5 minutes ago',
    product: 'Sentinel',
  },
  {
    id: '3',
    type: 'maintenance',
    message: 'BankConnect scheduled maintenance started',
    timestamp: '1 hour ago',
    product: 'BankConnect',
  },
  {
    id: '4',
    type: 'policy_updated',
    message: 'Risk assessment policy updated in Lisa',
    timestamp: '2 hours ago',
    product: 'Lisa',
  },
];

interface MetricCardProps {
  title: string;
  value: number | string;
  icon: React.ReactElement;
  color: string;
  subtitle?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, icon, color, subtitle }) => (
  <Card
    sx={{
      height: '100%',
      border: '1px solid #d1d9e0',
      borderRadius: '6px',
      boxShadow: 'none',
      '&:hover': {
        borderColor: '#0969da',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
      },
      transition: 'all 0.2s ease'
    }}
  >
    <CardContent sx={{ p: 3 }}>
      <Box display="flex" alignItems="center" justifyContent="space-between">
        <Box>
          <Typography
            sx={{
              color: '#656d76',
              fontSize: '0.875rem',
              fontWeight: 500,
              mb: 1
            }}
          >
            {title}
          </Typography>
          <Typography
            variant="h4"
            component="div"
            sx={{
              color: '#24292f',
              fontWeight: 600,
              fontSize: '2rem',
              mb: subtitle ? 0.5 : 0
            }}
          >
            {typeof value === 'number' ? value.toLocaleString() : value}
          </Typography>
          {subtitle && (
            <Typography
              variant="body2"
              sx={{ color: '#656d76', fontSize: '0.75rem' }}
            >
              {subtitle}
            </Typography>
          )}
        </Box>
        <Avatar
          sx={{
            width: 48,
            height: 48,
            backgroundColor: color,
            color: 'white'
          }}
        >
          {icon}
        </Avatar>
      </Box>
    </CardContent>
  </Card>
);

interface ProductCardProps {
  product: typeof mockProducts[0];
}

const ProductCard: React.FC<ProductCardProps> = ({ product }) => (
  <Card
    sx={{
      border: '1px solid #d1d9e0',
      borderRadius: '6px',
      boxShadow: 'none',
      '&:hover': {
        borderColor: '#0969da',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
      },
      transition: 'all 0.2s ease'
    }}
  >
    <CardContent sx={{ p: 3 }}>
      <Box display="flex" alignItems="flex-start" justifyContent="space-between" mb={2}>
        <Box display="flex" alignItems="center" gap={2}>
          <Avatar
            sx={{
              width: 32,
              height: 32,
              backgroundColor: product.color,
              color: 'white'
            }}
          >
            {product.icon}
          </Avatar>
          <Box>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                fontSize: '1rem',
                color: '#24292f',
                mb: 0.5
              }}
            >
              {product.name}
            </Typography>
            <Box display="flex" alignItems="center" gap={1}>
              <Chip
                label={product.type.toUpperCase()}
                size="small"
                sx={{
                  backgroundColor: product.color,
                  color: 'white',
                  fontWeight: 500,
                  fontSize: '0.7rem',
                  height: 18
                }}
              />
              <Box display="flex" alignItems="center" gap={0.5}>
                <CircleIcon
                  sx={{
                    fontSize: 8,
                    color: product.status === 'active' ? '#1f883d' : '#fb8500'
                  }}
                />
                <Typography
                  variant="caption"
                  sx={{
                    color: product.status === 'active' ? '#1f883d' : '#fb8500',
                    fontWeight: 500,
                    textTransform: 'capitalize',
                    fontSize: '0.75rem'
                  }}
                >
                  {product.status}
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>
        <Tooltip title="More options">
          <IconButton
            size="small"
            sx={{
              color: '#656d76',
              '&:hover': { color: '#0969da', backgroundColor: '#f6f8fa' }
            }}
          >
            <MoreIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>

      <Grid container spacing={2}>
        <Grid item xs={4}>
          <Typography variant="body2" sx={{ color: '#656d76', fontSize: '0.75rem' }}>
            Policies
          </Typography>
          <Typography variant="h6" sx={{ color: '#24292f', fontWeight: 600 }}>
            {product.policies}
          </Typography>
        </Grid>
        <Grid item xs={4}>
          <Typography variant="body2" sx={{ color: '#656d76', fontSize: '0.75rem' }}>
            Decisions (24h)
          </Typography>
          <Typography variant="h6" sx={{ color: '#24292f', fontWeight: 600 }}>
            {product.decisions24h}
          </Typography>
        </Grid>
        <Grid item xs={4}>
          <Typography variant="body2" sx={{ color: '#656d76', fontSize: '0.75rem' }}>
            Uptime
          </Typography>
          <Typography
            variant="h6"
            sx={{
              color: product.uptime >= 99.5 ? '#1f883d' : product.uptime >= 99 ? '#fb8500' : '#da3633',
              fontWeight: 600
            }}
          >
            {product.uptime}%
          </Typography>
        </Grid>
      </Grid>

      <Box mt={2}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
          <Typography variant="body2" sx={{ color: '#656d76', fontSize: '0.75rem' }}>
            Performance
          </Typography>
          <Typography variant="body2" sx={{ color: '#656d76', fontSize: '0.75rem' }}>
            {product.uptime}%
          </Typography>
        </Box>
        <LinearProgress
          variant="determinate"
          value={product.uptime}
          sx={{
            height: 6,
            borderRadius: 3,
            backgroundColor: '#f6f8fa',
            '& .MuiLinearProgress-bar': {
              backgroundColor: product.uptime >= 99.5 ? '#1f883d' : product.uptime >= 99 ? '#fb8500' : '#da3633',
              borderRadius: 3,
            },
          }}
        />
      </Box>
    </CardContent>
  </Card>
);

const DashboardPage: React.FC = () => {
  return (
    <Box sx={{ p: 3 }}>
      {/* Header Section - GitHub Style */}
      <Box sx={{ mb: 4 }}>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
          <Box>
            <Typography
              variant="h4"
              component="h1"
              sx={{
                fontWeight: 600,
                fontSize: '2rem',
                color: '#24292f',
                mb: 1
              }}
            >
              Finbox Products
            </Typography>
            <Typography
              variant="body1"
              sx={{
                color: '#656d76',
                fontSize: '1rem'
              }}
            >
              Monitor and manage your financial technology products, policies, and security decisions
            </Typography>
          </Box>
          <Tooltip title="Refresh dashboard">
            <IconButton
              sx={{
                color: '#656d76',
                '&:hover': { color: '#0969da', backgroundColor: '#f6f8fa' }
              }}
            >
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Active Products"
            value={mockMetrics.totalProducts}
            icon={<ProductsIcon />}
            color="#0969da"
            subtitle="Financial services"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Security Policies"
            value={mockMetrics.totalPolicies}
            icon={<PoliciesIcon />}
            color="#8250df"
            subtitle="Across all products"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Decisions Today"
            value={mockMetrics.totalDecisions}
            icon={<DecisionsIcon />}
            color="#1f883d"
            subtitle="Policy evaluations"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Avg Performance"
            value="99.4%"
            icon={<TrendingUpIcon />}
            color="#fb8500"
            subtitle="System uptime"
          />
        </Grid>
      </Grid>

      {/* Products Overview */}
      <Box sx={{ mb: 4 }}>
        <Typography
          variant="h5"
          sx={{
            fontWeight: 600,
            color: '#24292f',
            mb: 3
          }}
        >
          Product Status
        </Typography>
        <Grid container spacing={3}>
          {mockProducts.map((product) => (
            <Grid item xs={12} md={6} lg={4} key={product.id}>
              <ProductCard product={product} />
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* Health and Policy Status */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card
            sx={{
              border: '1px solid #d1d9e0',
              borderRadius: '6px',
              boxShadow: 'none'
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  color: '#24292f',
                  mb: 3
                }}
              >
                Product Health
              </Typography>
              <Stack spacing={2}>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box display="flex" alignItems="center" gap={1}>
                    <CircleIcon sx={{ fontSize: 8, color: '#1f883d' }} />
                    <Typography variant="body2" sx={{ color: '#24292f' }}>
                      Healthy
                    </Typography>
                  </Box>
                  <Chip
                    label={mockMetrics.productsHealth.healthy}
                    size="small"
                    sx={{
                      backgroundColor: '#dcfce7',
                      color: '#16a34a',
                      fontWeight: 600
                    }}
                  />
                </Box>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box display="flex" alignItems="center" gap={1}>
                    <CircleIcon sx={{ fontSize: 8, color: '#fb8500' }} />
                    <Typography variant="body2" sx={{ color: '#24292f' }}>
                      Warning
                    </Typography>
                  </Box>
                  <Chip
                    label={mockMetrics.productsHealth.warning}
                    size="small"
                    sx={{
                      backgroundColor: '#fef3c7',
                      color: '#d97706',
                      fontWeight: 600
                    }}
                  />
                </Box>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box display="flex" alignItems="center" gap={1}>
                    <CircleIcon sx={{ fontSize: 8, color: '#da3633' }} />
                    <Typography variant="body2" sx={{ color: '#24292f' }}>
                      Critical
                    </Typography>
                  </Box>
                  <Chip
                    label={mockMetrics.productsHealth.critical}
                    size="small"
                    sx={{
                      backgroundColor: '#fef2f2',
                      color: '#dc2626',
                      fontWeight: 600
                    }}
                  />
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card
            sx={{
              border: '1px solid #d1d9e0',
              borderRadius: '6px',
              boxShadow: 'none'
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  color: '#24292f',
                  mb: 3
                }}
              >
                Policy Distribution
              </Typography>
              <Stack spacing={2}>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box display="flex" alignItems="center" gap={1}>
                    <CircleIcon sx={{ fontSize: 8, color: '#1f883d' }} />
                    <Typography variant="body2" sx={{ color: '#24292f' }}>
                      Active
                    </Typography>
                  </Box>
                  <Chip
                    label={mockMetrics.policyDistribution.active}
                    size="small"
                    sx={{
                      backgroundColor: '#dcfce7',
                      color: '#16a34a',
                      fontWeight: 600
                    }}
                  />
                </Box>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box display="flex" alignItems="center" gap={1}>
                    <CircleIcon sx={{ fontSize: 8, color: '#0969da' }} />
                    <Typography variant="body2" sx={{ color: '#24292f' }}>
                      Draft
                    </Typography>
                  </Box>
                  <Chip
                    label={mockMetrics.policyDistribution.draft}
                    size="small"
                    sx={{
                      backgroundColor: '#dbeafe',
                      color: '#2563eb',
                      fontWeight: 600
                    }}
                  />
                </Box>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box display="flex" alignItems="center" gap={1}>
                    <CircleIcon sx={{ fontSize: 8, color: '#656d76' }} />
                    <Typography variant="body2" sx={{ color: '#24292f' }}>
                      Deprecated
                    </Typography>
                  </Box>
                  <Chip
                    label={mockMetrics.policyDistribution.deprecated}
                    size="small"
                    sx={{
                      backgroundColor: '#f3f4f6',
                      color: '#6b7280',
                      fontWeight: 600
                    }}
                  />
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Activity */}
      <Card
        sx={{
          border: '1px solid #d1d9e0',
          borderRadius: '6px',
          boxShadow: 'none'
        }}
      >
        <CardContent sx={{ p: 3 }}>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: '#24292f',
              mb: 3
            }}
          >
            Recent Activity
          </Typography>
          <Stack spacing={2}>
            {recentActivity.map((activity) => (
              <Box key={activity.id} display="flex" alignItems="flex-start" gap={2}>
                <Box
                  sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: activity.type === 'security_alert' ? '#da3633' :
                                   activity.type === 'maintenance' ? '#fb8500' : '#1f883d',
                    mt: 1,
                    flexShrink: 0
                  }}
                />
                <Box flex={1}>
                  <Typography variant="body2" sx={{ color: '#24292f', mb: 0.5 }}>
                    {activity.message}
                  </Typography>
                  <Typography variant="caption" sx={{ color: '#656d76' }}>
                    {activity.product} • {activity.timestamp}
                  </Typography>
                </Box>
              </Box>
            ))}
          </Stack>
        </CardContent>
      </Card>
    </Box>
  );
};

export default DashboardPage;
