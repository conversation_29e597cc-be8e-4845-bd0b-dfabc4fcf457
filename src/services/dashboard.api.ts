import { api } from '@/lib/api';
import { DashboardMetrics } from '@/types';
import { API_ENDPOINTS } from '@/constants';

export interface DashboardSummary {
  totalSystems: number;
  totalPolicies: number;
  totalDecisions: number;
  recentActivity: {
    id: string;
    type: 'policy_deployed' | 'system_added' | 'decision_spike' | 'error';
    message: string;
    timestamp: string;
    severity: 'info' | 'warning' | 'error' | 'success';
  }[];
}

export interface SystemHealthMetrics {
  systemId: string;
  systemName: string;
  status: 'healthy' | 'warning' | 'critical';
  uptime: number;
  responseTime: number;
  errorRate: number;
  lastCheck: string;
}

export interface PolicyPerformanceMetrics {
  policyId: string;
  policyName: string;
  systemName: string;
  executionCount: number;
  averageExecutionTime: number;
  errorRate: number;
  lastExecuted: string;
}

export const dashboardApi = {
  // Get dashboard summary data
  getDashboardSummary: async (): Promise<DashboardSummary> => {
    return api.get<DashboardSummary>(API_ENDPOINTS.DASHBOARD_SUMMARY);
  },

  // Get comprehensive dashboard metrics
  getDashboardMetrics: async (): Promise<DashboardMetrics> => {
    return api.get<DashboardMetrics>(API_ENDPOINTS.DASHBOARD_METRICS);
  },

  // Get system health metrics
  getSystemHealthMetrics: async (): Promise<SystemHealthMetrics[]> => {
    return api.get<SystemHealthMetrics[]>(`${API_ENDPOINTS.DASHBOARD_METRICS}/systems-health`);
  },

  // Get policy performance metrics
  getPolicyPerformanceMetrics: async (
    limit: number = 10
  ): Promise<PolicyPerformanceMetrics[]> => {
    return api.get<PolicyPerformanceMetrics[]>(
      `${API_ENDPOINTS.DASHBOARD_METRICS}/policy-performance?limit=${limit}`
    );
  },

  // Get decision volume trends
  getDecisionVolumeTrends: async (
    period: '24h' | '7d' | '30d' = '24h'
  ): Promise<{
    labels: string[];
    allowed: number[];
    denied: number[];
  }> => {
    return api.get<{
      labels: string[];
      allowed: number[];
      denied: number[];
    }>(`${API_ENDPOINTS.DASHBOARD_METRICS}/decision-trends?period=${period}`);
  },

  // Get top systems by decision volume
  getTopSystemsByDecisions: async (
    limit: number = 5
  ): Promise<{
    systemId: string;
    systemName: string;
    decisionCount: number;
    allowedPercentage: number;
  }[]> => {
    return api.get<{
      systemId: string;
      systemName: string;
      decisionCount: number;
      allowedPercentage: number;
    }[]>(`${API_ENDPOINTS.DASHBOARD_METRICS}/top-systems?limit=${limit}`);
  },

  // Get recent errors and alerts
  getRecentAlerts: async (
    limit: number = 10
  ): Promise<{
    id: string;
    type: 'policy_error' | 'system_down' | 'high_latency' | 'auth_failure';
    message: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    timestamp: string;
    systemId?: string;
    policyId?: string;
    resolved: boolean;
  }[]> => {
    return api.get<{
      id: string;
      type: 'policy_error' | 'system_down' | 'high_latency' | 'auth_failure';
      message: string;
      severity: 'low' | 'medium' | 'high' | 'critical';
      timestamp: string;
      systemId?: string;
      policyId?: string;
      resolved: boolean;
    }[]>(`${API_ENDPOINTS.DASHBOARD_METRICS}/alerts?limit=${limit}`);
  },
};
