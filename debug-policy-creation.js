// Debug Policy Creation Issues
console.log('🔍 Debugging Policy Creation...');

// Wait for page to load
setTimeout(() => {
  console.log('📍 Page loaded, checking elements...');
  
  // Check if Create Policy button exists
  const createButtons = document.querySelectorAll('button');
  const createPolicyButton = Array.from(createButtons).find(btn => 
    btn.textContent?.includes('Create Policy') || 
    btn.textContent?.includes('Create') ||
    btn.getAttribute('aria-label')?.includes('Create')
  );
  
  if (createPolicyButton) {
    console.log('✅ Create Policy button found:', createPolicyButton);
    console.log('📝 Button text:', createPolicyButton.textContent);
    console.log('🎯 Button attributes:', createPolicyButton.attributes);
    
    // Check if button is clickable
    const isDisabled = createPolicyButton.disabled || createPolicyButton.getAttribute('disabled');
    console.log('🔘 Button disabled:', isDisabled);
    
    // Try to click the button
    console.log('🖱️ Attempting to click Create Policy button...');
    try {
      createPolicyButton.click();
      console.log('✅ Button clicked successfully');
      
      // Check if dialog opened
      setTimeout(() => {
        const dialogs = document.querySelectorAll('[role="dialog"], .MuiDialog-root');
        console.log('📋 Dialogs found after click:', dialogs.length);
        
        if (dialogs.length > 0) {
          console.log('✅ Dialog opened successfully');
          dialogs.forEach((dialog, index) => {
            console.log(`📋 Dialog ${index}:`, dialog);
          });
        } else {
          console.log('❌ No dialog opened - checking for errors...');
          
          // Check for any error messages
          const errors = document.querySelectorAll('.MuiAlert-root, [role="alert"], .error');
          console.log('🚨 Error elements found:', errors.length);
          errors.forEach((error, index) => {
            console.log(`🚨 Error ${index}:`, error.textContent);
          });
        }
      }, 1000);
      
    } catch (error) {
      console.error('❌ Error clicking button:', error);
    }
    
  } else {
    console.log('❌ Create Policy button not found');
    console.log('🔍 Available buttons:');
    createButtons.forEach((btn, index) => {
      console.log(`  ${index}: "${btn.textContent?.trim()}" (${btn.className})`);
    });
  }
  
  // Check for any JavaScript errors
  const originalError = console.error;
  const originalWarn = console.warn;
  let errorCount = 0;
  let warnCount = 0;
  
  console.error = function(...args) {
    errorCount++;
    console.log(`🚨 JS Error ${errorCount}:`, ...args);
    originalError.apply(console, args);
  };
  
  console.warn = function(...args) {
    warnCount++;
    console.log(`⚠️ JS Warning ${warnCount}:`, ...args);
    originalWarn.apply(console, args);
  };
  
}, 2000);

// Check for React errors
window.addEventListener('error', (event) => {
  console.error('🚨 Global Error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('🚨 Unhandled Promise Rejection:', event.reason);
});

console.log('🔍 Debug script loaded. Waiting for page to load...');
