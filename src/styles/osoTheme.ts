import { createTheme } from '@mui/material/styles';

// OSO-inspired color palette (limited to 7 colors)
const osoColors = {
  // Primary colors
  primary: '#2563eb', // Blue - main action color
  primaryLight: '#3b82f6',
  
  // Secondary/accent
  accent: '#7c3aed', // Purple - secondary actions
  
  // Status colors
  success: '#059669', // Green - success states
  warning: '#d97706', // Orange - warnings
  error: '#dc2626', // Red - errors/destructive actions
  
  // Neutral colors
  neutral: '#6b7280', // Gray - text and borders
  
  // Background colors (derived from neutrals)
  background: '#ffffff',
  surface: '#f9fafb',
  border: '#e5e7eb',
  textPrimary: '#111827',
  textSecondary: '#6b7280',
  textMuted: '#9ca3af',
};

// OSO-inspired typography
const osoTypography = {
  fontFamily: [
    '-apple-system',
    'BlinkMacSystemFont',
    '"Segoe UI"',
    'Roboto',
    '"Helvetica Neue"',
    'Arial',
    'sans-serif',
  ].join(','),
  
  // Font weights
  fontWeightLight: 300,
  fontWeightRegular: 400,
  fontWeightMedium: 500,
  fontWeightBold: 600,
  
  // Font sizes (OSO uses clean, readable sizes)
  h1: {
    fontSize: '2.25rem', // 36px
    fontWeight: 600,
    lineHeight: 1.2,
    letterSpacing: '-0.025em',
  },
  h2: {
    fontSize: '1.875rem', // 30px
    fontWeight: 600,
    lineHeight: 1.3,
    letterSpacing: '-0.025em',
  },
  h3: {
    fontSize: '1.5rem', // 24px
    fontWeight: 600,
    lineHeight: 1.3,
  },
  h4: {
    fontSize: '1.25rem', // 20px
    fontWeight: 600,
    lineHeight: 1.4,
  },
  h5: {
    fontSize: '1.125rem', // 18px
    fontWeight: 600,
    lineHeight: 1.4,
  },
  h6: {
    fontSize: '1rem', // 16px
    fontWeight: 600,
    lineHeight: 1.5,
  },
  body1: {
    fontSize: '1rem', // 16px
    fontWeight: 400,
    lineHeight: 1.5,
  },
  body2: {
    fontSize: '0.875rem', // 14px
    fontWeight: 400,
    lineHeight: 1.5,
  },
  caption: {
    fontSize: '0.75rem', // 12px
    fontWeight: 400,
    lineHeight: 1.4,
    color: osoColors.textMuted,
  },
};

// Create the OSO-inspired theme
export const osoTheme = createTheme({
  palette: {
    primary: {
      main: osoColors.primary,
      light: osoColors.primaryLight,
      dark: '#1d4ed8',
      contrastText: '#ffffff',
    },
    secondary: {
      main: osoColors.accent,
      light: '#8b5cf6',
      dark: '#6d28d9',
      contrastText: '#ffffff',
    },
    success: {
      main: osoColors.success,
      light: '#10b981',
      dark: '#047857',
      contrastText: '#ffffff',
    },
    warning: {
      main: osoColors.warning,
      light: '#f59e0b',
      dark: '#b45309',
      contrastText: '#ffffff',
    },
    error: {
      main: osoColors.error,
      light: '#ef4444',
      dark: '#b91c1c',
      contrastText: '#ffffff',
    },
    grey: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: osoColors.neutral,
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
    },
    background: {
      default: osoColors.background,
      paper: osoColors.surface,
    },
    text: {
      primary: osoColors.textPrimary,
      secondary: osoColors.textSecondary,
    },
  },
  
  typography: osoTypography,
  
  shape: {
    borderRadius: 8, // OSO uses subtle rounded corners
  },
  
  spacing: 8, // 8px base spacing unit
  
  components: {
    // Button styling to match OSO
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none', // OSO doesn't use uppercase
          fontWeight: 500,
          borderRadius: 8,
          padding: '8px 16px',
          fontSize: '0.875rem',
          boxShadow: 'none',
          '&:hover': {
            boxShadow: 'none',
          },
        },
        contained: {
          backgroundColor: osoColors.primary,
          color: '#ffffff',
          '&:hover': {
            backgroundColor: '#1d4ed8',
          },
        },
        outlined: {
          borderColor: osoColors.border,
          color: osoColors.textPrimary,
          '&:hover': {
            backgroundColor: osoColors.surface,
            borderColor: osoColors.neutral,
          },
        },
        text: {
          color: osoColors.textSecondary,
          '&:hover': {
            backgroundColor: osoColors.surface,
          },
        },
      },
    },
    
    // Card styling
    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
          borderRadius: 12,
          border: `1px solid ${osoColors.border}`,
        },
      },
    },
    
    // Paper styling
    MuiPaper: {
      styleOverrides: {
        root: {
          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
          borderRadius: 8,
        },
        outlined: {
          border: `1px solid ${osoColors.border}`,
        },
      },
    },
    
    // TextField styling
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 8,
            '& fieldset': {
              borderColor: osoColors.border,
            },
            '&:hover fieldset': {
              borderColor: osoColors.neutral,
            },
            '&.Mui-focused fieldset': {
              borderColor: osoColors.primary,
              borderWidth: 2,
            },
          },
        },
      },
    },
    
    // Chip styling
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 6,
          fontSize: '0.75rem',
          fontWeight: 500,
        },
        outlined: {
          borderColor: osoColors.border,
        },
      },
    },
    
    // Stepper styling
    MuiStepper: {
      styleOverrides: {
        root: {
          padding: '24px 0',
        },
      },
    },
    
    MuiStepLabel: {
      styleOverrides: {
        label: {
          fontSize: '0.875rem',
          fontWeight: 500,
          '&.Mui-active': {
            color: osoColors.primary,
            fontWeight: 600,
          },
          '&.Mui-completed': {
            color: osoColors.success,
          },
        },
      },
    },
    
    // Dialog styling
    MuiDialog: {
      styleOverrides: {
        paper: {
          borderRadius: 12,
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        },
      },
    },
    
    // Accordion styling
    MuiAccordion: {
      styleOverrides: {
        root: {
          boxShadow: 'none',
          border: `1px solid ${osoColors.border}`,
          borderRadius: 8,
          '&:before': {
            display: 'none',
          },
          '&.Mui-expanded': {
            margin: 0,
          },
        },
      },
    },
  },
});

// Export color constants for use in components
export const OSO_COLORS = osoColors;
