import { api } from '@/lib/api';
import { System, CreateSystemForm, PaginatedResponse } from '@/types';
import { API_ENDPOINTS } from '@/constants';

export interface SystemsQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  type?: string;
  status?: string;
}

export const systemsApi = {
  // Get all systems with optional filtering and pagination
  getSystems: async (params?: SystemsQueryParams): Promise<PaginatedResponse<System>> => {
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.type) queryParams.append('type', params.type);
    if (params?.status) queryParams.append('status', params.status);
    
    const url = `${API_ENDPOINTS.PRODUCTS}?${queryParams.toString()}`;
    return api.get<PaginatedResponse<System>>(url);
  },

  // Get system by ID
  getSystemById: async (id: string): Promise<System> => {
    return api.get<System>(API_ENDPOINTS.PRODUCT_BY_ID(id));
  },

  // Create new system
  createSystem: async (data: CreateSystemForm): Promise<System> => {
    return api.post<System>(API_ENDPOINTS.PRODUCTS, data);
  },

  // Update system
  updateSystem: async (id: string, data: Partial<CreateSystemForm>): Promise<System> => {
    return api.put<System>(API_ENDPOINTS.PRODUCT_BY_ID(id), data);
  },

  // Delete system
  deleteSystem: async (id: string): Promise<void> => {
    return api.delete<void>(API_ENDPOINTS.PRODUCT_BY_ID(id));
  },

  // Get system health status
  getSystemHealth: async (id: string): Promise<{ status: string; lastCheck: string }> => {
    return api.get<{ status: string; lastCheck: string }>(`${API_ENDPOINTS.PRODUCT_BY_ID(id)}/health`);
  },
};
