import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  IconButton,
  Button,
  Collap<PERSON>,
  <PERSON>ert,
  <PERSON>ltip,
  Chip,
  Card,
  CardContent,
  CardHeader,
  Avatar,
  LinearProgress,
  Stack,
  Badge,
} from '@mui/material';
import {
  Code as CodeIcon,
  ContentCopy as CopyIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,

  VisibilityOff as VisibilityOffIcon,
  Check as CheckIcon,
  Security as SecurityIcon,
  Info as InfoIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  CheckCircle as CheckCircleIcon,

} from '@mui/icons-material';
// Removed Monaco Editor import - using custom JSFiddle-style preview
import { PolicyParser } from '@/lib/policyParser';
import { PolicyOutput } from '@/types';
import { OSO_COLORS } from '@/styles/osoTheme';

interface OpaCodeSidebarProps {
  taskTypes: Record<string, any>;
  globalSettings: {
    addConditionButton: boolean;
    allowUserTypeCheck: boolean;
    allowedUserTypes: string[];
  };
  packageName: string;
  isVisible: boolean;
  onToggleVisibility: () => void;
}

const OpaCodeSidebar: React.FC<OpaCodeSidebarProps> = ({
  taskTypes,
  globalSettings,
  packageName,
  onToggleVisibility,
}) => {
  const [generatedCode, setGeneratedCode] = useState<string>('# Loading...');
  const [copySuccess, setCopySuccess] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState<Date>(new Date());
  const [expandedSections, setExpandedSections] = useState({
    preview: true,
    explanation: false,
  });

  useEffect(() => {
    generateOpaCode();
  }, [taskTypes, globalSettings, packageName]);

  const generateOpaCode = async () => {
    setIsGenerating(true);

    try {
      const output: PolicyOutput = {
        taskTypes: taskTypes || {},
        buttons: {
          addConditionButton: globalSettings?.addConditionButton || false,
        },
      };

      let policy = `package ${packageName || 'policy'}\n\n`;

      if (globalSettings?.allowUserTypeCheck) {
        policy += `user_type_allowed(allowed_user_types) if input.user.type in allowed_user_types\n\n`;
        policy += `else := false\n\n`;
      }

      try {
        const generatedOutput = PolicyParser.generateOutputString(output);
        policy += generatedOutput;
      } catch (error) {
        console.error('Error generating policy:', error);
        policy += `# Error generating policy output
# Please check your task type configuration

output := {
  "taskTypes": {},
  "buttons": {
    "addConditionButton": ${globalSettings?.addConditionButton || false}
  }
}`;
      }

      setGeneratedCode(policy);
      setLastUpdateTime(new Date());
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(generatedCode);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  // Enhanced JSFiddle-style syntax highlighting for Rego code
  const highlightRegoCode = (code: string): React.ReactNode => {
    const lines = code.split('\n');

    return lines.map((line, lineIndex) => {
      let highlightedLine = line;

      // Apply syntax highlighting in order of precedence

      // 1. Comments (must be first to avoid highlighting inside comments)
      highlightedLine = highlightedLine.replace(/(#.*$)/g,
        '<span style="color: #6a737d; font-style: italic;">$1</span>');

      // 2. Strings (must be before keywords to avoid highlighting inside strings)
      highlightedLine = highlightedLine.replace(/"([^"]*)"/g,
        '<span style="color: #032f62; background-color: #f6f8fa; padding: 0 2px; border-radius: 2px;">"$1"</span>');

      // 3. Rego keywords
      highlightedLine = highlightedLine.replace(/\b(package|import|default|if|else|not|some|every|with|as|in|contains|startswith|endswith|count|sum|max|min|sort|output)\b/g,
        '<span style="color: #d73a49; font-weight: 600;">$1</span>');

      // 4. Built-in functions and types
      highlightedLine = highlightedLine.replace(/\b(input|data|allow|deny|user_type_allowed)\b/g,
        '<span style="color: #6f42c1; font-weight: 500;">$1</span>');

      // 5. Operators and symbols
      highlightedLine = highlightedLine.replace(/(:=|==|!=|<=|>=|<|>|\|\||&&|\+|\-|\*|\/|%)/g,
        '<span style="color: #e36209; font-weight: 500;">$1</span>');

      // 6. Boolean values
      highlightedLine = highlightedLine.replace(/\b(true|false)\b/g,
        '<span style="color: #005cc5; font-weight: 600;">$1</span>');

      // 7. Numbers
      highlightedLine = highlightedLine.replace(/\b(\d+)\b/g,
        '<span style="color: #005cc5;">$1</span>');

      // 8. Object keys and properties
      highlightedLine = highlightedLine.replace(/(\w+)(\s*:)/g,
        '<span style="color: #22863a; font-weight: 500;">$1</span>$2');

      const isEmptyLine = line.trim() === '';
      const isCommentLine = line.trim().startsWith('#');

      return (
        <Box
          key={lineIndex}
          sx={{
            display: 'flex',
            minHeight: '1.4em',
            lineHeight: '1.4em',
            backgroundColor: isEmptyLine ? 'transparent' :
                           isCommentLine ? '#f6f8fa' :
                           lineIndex % 2 === 0 ? '#fafbfc' : 'transparent',
            borderLeft: isCommentLine ? '3px solid #28a745' : '3px solid transparent',
            paddingLeft: isCommentLine ? '8px' : '11px',
            transition: 'background-color 0.2s ease',
            '&:hover': {
              backgroundColor: '#f1f8ff',
            },
          }}
        >
          <span
            style={{
              color: '#586069',
              fontSize: '0.8em',
              width: '3em',
              textAlign: 'right',
              paddingRight: '1em',
              userSelect: 'none',
              flexShrink: 0,
              fontWeight: 500,
            }}
          >
            {lineIndex + 1}
          </span>
          <span
            style={{
              flex: 1,
              fontFamily: 'SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace',
              fontSize: '0.875rem',
              whiteSpace: 'pre',
              color: '#24292e',
            }}
            dangerouslySetInnerHTML={{ __html: highlightedLine || '&nbsp;' }}
          />
        </Box>
      );
    });
  };

  const getPermissionSummary = () => {
    const taskTypeCount = Object.keys(taskTypes).length;
    const totalStatuses = Object.values(taskTypes).reduce(
      (acc: number, taskType: any) => acc + Object.keys(taskType.status || {}).length,
      0
    );
    const totalButtons = Object.values(taskTypes).reduce(
      (acc: number, taskType: any) => {
        return acc + Object.values(taskType.status || {}).reduce(
          (statusAcc: number, status: any) => statusAcc + Object.keys(status.buttons || {}).length,
          0
        );
      },
      0
    );

    return { taskTypeCount, totalStatuses, totalButtons };
  };

  const { taskTypeCount, totalStatuses, totalButtons } = getPermissionSummary();

  // Since we're now using inline layout, we don't need the hidden state
  // The parent component controls visibility

  return (
    <Box
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        bgcolor: '#ffffff',
        overflow: 'hidden',
      }}
    >
      {/* Enhanced Header */}
      <CardHeader
        avatar={
          <Avatar
            sx={{
              bgcolor: OSO_COLORS.primary,
              color: 'white',
              width: 40,
              height: 40,
            }}
          >
            <CodeIcon />
          </Avatar>
        }
        title={
          <Box display="flex" alignItems="center" gap={1}>
            <Typography variant="h6" fontWeight={600} color={OSO_COLORS.textPrimary}>
              Live Code Preview
            </Typography>
            <Chip
              label="JSFiddle Style"
              size="small"
              variant="outlined"
              sx={{
                fontSize: '0.65rem',
                height: 20,
                color: OSO_COLORS.primary,
                borderColor: OSO_COLORS.primary,
              }}
            />
          </Box>
        }
        subheader={
          <Typography variant="body2" color="textSecondary">
            Real-time Rego policy generation with instant updates
          </Typography>
        }
        action={
          <Box display="flex" alignItems="center" gap={1}>
            <Tooltip title="Refresh Code">
              <IconButton
                size="small"
                onClick={generateOpaCode}
                sx={{ color: OSO_COLORS.textSecondary }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Hide Sidebar">
              <IconButton
                onClick={onToggleVisibility}
                size="small"
                sx={{ color: OSO_COLORS.textSecondary }}
              >
                <VisibilityOffIcon />
              </IconButton>
            </Tooltip>
          </Box>
        }
        sx={{
          bgcolor: `linear-gradient(135deg, ${OSO_COLORS.surface} 0%, #ffffff 100%)`,
          borderBottom: `1px solid ${OSO_COLORS.border}`,
        }}
      />

      {/* Enhanced Summary Stats */}
      <Box sx={{ px: 2, py: 1.5, bgcolor: OSO_COLORS.background }}>
        <Stack direction="row" spacing={1} justifyContent="center">
          <Badge badgeContent={taskTypeCount} color="primary">
            <Chip
              icon={<SecurityIcon />}
              label="Task Types"
              size="small"
              variant="outlined"
              color="primary"
            />
          </Badge>
          <Badge badgeContent={totalStatuses} color="secondary">
            <Chip
              icon={<InfoIcon />}
              label="Statuses"
              size="small"
              variant="outlined"
              color="secondary"
            />
          </Badge>
          <Badge badgeContent={totalButtons} color="success">
            <Chip
              icon={<CheckCircleIcon />}
              label="Permissions"
              size="small"
              variant="outlined"
              color="success"
            />
          </Badge>
        </Stack>

        {/* Progress Indicator */}
        <Box sx={{ mt: 1.5 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={0.5}>
            <Typography variant="caption" color="textSecondary">
              Configuration Progress
            </Typography>
            <Typography variant="caption" color="textSecondary">
              {totalButtons > 0 ? Math.round((totalButtons / (totalStatuses || 1)) * 100) : 0}%
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={totalButtons > 0 ? Math.min((totalButtons / (totalStatuses || 1)) * 100, 100) : 0}
            sx={{
              height: 4,
              borderRadius: 2,
              bgcolor: OSO_COLORS.border,
              '& .MuiLinearProgress-bar': {
                bgcolor: totalButtons > totalStatuses * 0.8 ? OSO_COLORS.success : OSO_COLORS.primary,
              },
            }}
          />
        </Box>
      </Box>

      {/* JSFiddle-style Action Bar */}
      <Box sx={{
        px: 2,
        py: 1.5,
        borderBottom: `1px solid ${OSO_COLORS.border}`,
        background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}>
        {/* Code Stats */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Typography variant="caption" sx={{ color: '#6a737d', fontWeight: 500 }}>
            {generatedCode.split('\n').length} lines
          </Typography>
          <Typography variant="caption" sx={{ color: '#6a737d', fontWeight: 500 }}>
            {generatedCode.length} chars
          </Typography>
          <Typography variant="caption" sx={{ color: '#6a737d', fontWeight: 500 }}>
            {Object.keys(taskTypes || {}).length} task types
          </Typography>
        </Box>

        {/* Action Buttons */}
        <Stack direction="row" spacing={1}>
          <Button
            size="small"
            startIcon={copySuccess ? <CheckIcon /> : <CopyIcon />}
            onClick={handleCopyCode}
            variant={copySuccess ? "contained" : "outlined"}
            color={copySuccess ? "success" : "primary"}
            sx={{
              textTransform: 'none',
              fontSize: '0.75rem',
              px: 1.5,
              py: 0.5,
              minWidth: 'auto',
              borderRadius: 2,
              boxShadow: copySuccess ? '0 2px 4px rgba(40, 167, 69, 0.3)' : 'none',
            }}
          >
            {copySuccess ? 'Copied!' : 'Copy'}
          </Button>
          <Button
            size="small"
            startIcon={<DownloadIcon />}
            variant="outlined"
            sx={{
              textTransform: 'none',
              fontSize: '0.75rem',
              px: 1.5,
              py: 0.5,
              minWidth: 'auto',
              borderRadius: 2,
            }}
            onClick={() => {
              const blob = new Blob([generatedCode], { type: 'text/plain' });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = `${packageName || 'policy'}.rego`;
              a.click();
              URL.revokeObjectURL(url);
            }}
          >
            Save
          </Button>
        </Stack>
      </Box>

      {/* Content Sections */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {/* Enhanced Code Preview Section */}
        <Card sx={{ m: 1.5, flex: 1, display: 'flex', flexDirection: 'column' }}>
          <CardHeader
            title={
              <Box display="flex" alignItems="center" gap={1}>
                <Typography variant="subtitle1" fontWeight={600}>
                  Live Rego Preview
                </Typography>
                <Box sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  bgcolor: isGenerating ? '#ffc107' : '#28a745',
                  animation: isGenerating ? 'blink 1s infinite' : 'none',
                  '@keyframes blink': {
                    '0%, 50%': { opacity: 1 },
                    '51%, 100%': { opacity: 0.3 },
                  },
                }} />
              </Box>
            }
            action={
              <Tooltip title="Refresh Code">
                <IconButton
                  size="small"
                  onClick={generateOpaCode}
                  disabled={isGenerating}
                  sx={{
                    color: isGenerating ? '#6c757d' : 'inherit',
                  }}
                >
                  <RefreshIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            }
            sx={{ pb: 1 }}
          />

          <CardContent sx={{ flex: 1, p: 0, '&:last-child': { pb: 0 } }}>
            {/* JSFiddle-style Live Preview */}
            <Box sx={{
              height: '100%',
              minHeight: 300,
              position: 'relative',
              overflow: 'hidden',
            }}>
              {/* Loading Indicator */}
              {isGenerating && (
                <Box sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: 2,
                  bgcolor: OSO_COLORS.primary,
                  animation: 'pulse 1s ease-in-out infinite',
                  zIndex: 1,
                  '@keyframes pulse': {
                    '0%': { opacity: 0.6 },
                    '50%': { opacity: 1 },
                    '100%': { opacity: 0.6 },
                  },
                }}/>
              )}

              {/* JSFiddle-style Header */}
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                px: 2,
                py: 1.5,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                fontSize: '0.75rem',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                  <Box sx={{
                    width: 10,
                    height: 10,
                    borderRadius: '50%',
                    bgcolor: isGenerating ? '#ffc107' : '#28a745',
                    boxShadow: isGenerating ? '0 0 8px rgba(255, 193, 7, 0.6)' : '0 0 8px rgba(40, 167, 69, 0.6)',
                    animation: isGenerating ? 'pulse 1.5s infinite' : 'none',
                    '@keyframes pulse': {
                      '0%': { transform: 'scale(1)', opacity: 1 },
                      '50%': { transform: 'scale(1.2)', opacity: 0.7 },
                      '100%': { transform: 'scale(1)', opacity: 1 },
                    },
                  }} />
                  <Typography variant="caption" sx={{ fontWeight: 600, color: 'white' }}>
                    {isGenerating ? 'Generating Code...' : 'Live Preview Active'}
                  </Typography>
                  <Chip
                    label="Rego"
                    size="small"
                    sx={{
                      height: 18,
                      fontSize: '0.6rem',
                      bgcolor: 'rgba(255,255,255,0.2)',
                      color: 'white',
                      fontWeight: 600,
                    }}
                  />
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="caption" sx={{ opacity: 0.9 }}>
                    Updated: {lastUpdateTime.toLocaleTimeString()}
                  </Typography>
                  <Box sx={{
                    width: 6,
                    height: 6,
                    borderRadius: '50%',
                    bgcolor: 'rgba(255,255,255,0.6)',
                  }} />
                </Box>
              </Box>

              {/* JSFiddle-style Code Display Area */}
              <Box sx={{
                height: 'calc(100% - 52px)',
                overflow: 'auto',
                bgcolor: '#fafbfc',
                border: '1px solid #e1e4e8',
                borderTop: 'none',
                fontFamily: 'SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace',
                fontSize: '0.875rem',
                lineHeight: 1.4,
                position: 'relative',
                '&::-webkit-scrollbar': {
                  width: 12,
                  height: 12,
                },
                '&::-webkit-scrollbar-track': {
                  bgcolor: '#f6f8fa',
                  borderRadius: 6,
                },
                '&::-webkit-scrollbar-thumb': {
                  bgcolor: '#d1d5da',
                  borderRadius: 6,
                  border: '2px solid #f6f8fa',
                  '&:hover': {
                    bgcolor: '#c6cbd1',
                  },
                },
                '&::-webkit-scrollbar-corner': {
                  bgcolor: '#f6f8fa',
                },
              }}>
                {/* Code Content */}
                <Box sx={{
                  minHeight: '100%',
                  position: 'relative',
                }}>
                  {generatedCode ? (
                    <Box sx={{
                      p: 0,
                      background: 'linear-gradient(90deg, #f6f8fa 0%, #ffffff 100%)',
                    }}>
                      {highlightRegoCode(generatedCode)}
                    </Box>
                  ) : (
                    <Box sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      height: 300,
                      color: '#586069',
                      textAlign: 'center',
                      p: 3,
                    }}>
                      <CodeIcon sx={{ fontSize: 48, color: '#d1d5da', mb: 2 }} />
                      <Typography variant="h6" sx={{ color: '#586069', mb: 1 }}>
                        No code generated yet
                      </Typography>
                      <Typography variant="body2" sx={{ color: '#6a737d', maxWidth: 300 }}>
                        Configure task types and permissions to see the generated Rego policy code appear here in real-time.
                      </Typography>
                    </Box>
                  )}

                  {/* JSFiddle-style watermark */}
                  <Box sx={{
                    position: 'absolute',
                    bottom: 8,
                    right: 8,
                    opacity: 0.3,
                    fontSize: '0.7rem',
                    color: '#6a737d',
                    fontStyle: 'italic',
                    pointerEvents: 'none',
                  }}>
                    Live Preview
                  </Box>
                </Box>
              </Box>
            </Box>
          </CardContent>
        </Card>

        {/* Enhanced Explanation Section */}
        <Card sx={{ m: 1.5, mt: 0 }}>
          <CardHeader
            title={
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="subtitle1" fontWeight={600}>
                  Code Explanation
                </Typography>
                <IconButton
                  size="small"
                  onClick={() => toggleSection('explanation')}
                >
                  {expandedSections.explanation ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              </Box>
            }
            sx={{ pb: 1, cursor: 'pointer' }}
            onClick={() => toggleSection('explanation')}
          />

          <Collapse in={expandedSections.explanation}>
            <CardContent>
              <Alert
                severity="info"
                sx={{
                  mb: 2,
                  bgcolor: `${OSO_COLORS.primary}08`,
                  border: `1px solid ${OSO_COLORS.primary}20`,
                }}
              >
                This OPA policy automatically updates as you modify task types and permissions above.
              </Alert>

              <Stack spacing={1.5}>
                <Box>
                  <Typography variant="subtitle2" fontWeight={600} gutterBottom>
                    Package Declaration
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Defines the policy namespace for organization
                  </Typography>
                </Box>

                {globalSettings.allowUserTypeCheck && (
                  <Box>
                    <Typography variant="subtitle2" fontWeight={600} gutterBottom>
                      User Type Validation
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Validates if user type is in the allowed list
                    </Typography>
                  </Box>
                )}

                <Box>
                  <Typography variant="subtitle2" fontWeight={600} gutterBottom>
                    Permission Structure
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Defines structured permissions for each task type and status combination
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Collapse>
        </Card>
      </Box>
    </Box>
  );
};

export default OpaCodeSidebar;
