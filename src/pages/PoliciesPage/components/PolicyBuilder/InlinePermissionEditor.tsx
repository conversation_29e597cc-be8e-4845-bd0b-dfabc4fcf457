import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Tooltip,
  Alert,

  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Avatar,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,

  Person as PersonIcon,
  Group as GroupIcon,
  Public as PublicIcon,
  Code as CodeIcon,
} from '@mui/icons-material';
import { PolicyPermission, ButtonCondition } from '@/types';

interface InlinePermissionEditorProps {
  permissions: PolicyPermission[];
  onChange: (permissions: PolicyPermission[]) => void;
  productId?: string;
}

const PERMISSION_TEMPLATES = [
  {
    id: 'creator-only',
    name: 'Creator Only',
    description: 'Only the person who created the resource can perform this action',
    icon: <PersonIcon />,
    condition: { createdBy: 'currentUser' },
    color: '#0969da',
  },
  {
    id: 'creator-and-managers',
    name: 'Creator & Managers',
    description: 'Creator and users with higher privileges can perform this action',
    icon: <GroupIcon />,
    condition: { createdBy: 'currentUserAndAbove' },
    color: '#1f883d',
  },
  {
    id: 'public-access',
    name: 'Public Access',
    description: 'Any authenticated user can perform this action',
    icon: <PublicIcon />,
    condition: { createdBy: 'AnyUser' },
    color: '#8250df',
  },
  {
    id: 'custom-rule',
    name: 'Custom Rule',
    description: 'Define your own custom access control logic',
    icon: <CodeIcon />,
    condition: { custom: 'input.user.role == "admin"' },
    color: '#d1242f',
  },
];

const COMMON_ACTIONS = [
  'view', 'create', 'edit', 'delete', 'approve', 'reject', 'submit', 'review', 'publish'
];

const COMMON_RESOURCES = [
  'loan-application', 'transaction', 'user-account', 'bank-account', 'payment', 'document', 'report'
];

const InlinePermissionEditor: React.FC<InlinePermissionEditorProps> = ({
  permissions,
  onChange,
}) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingPermission, setEditingPermission] = useState<PolicyPermission | null>(null);
  const [newPermission, setNewPermission] = useState<Partial<PolicyPermission>>({
    name: '',
    action: '',
    resource: '',
    condition: { createdBy: 'currentUser' },
    description: '',
  });

  const handleAddPermission = () => {
    if (newPermission.name && newPermission.action && newPermission.resource) {
      const permission: PolicyPermission = {
        id: `perm-${Date.now()}`,
        name: newPermission.name,
        action: newPermission.action,
        resource: newPermission.resource,
        condition: newPermission.condition || { createdBy: 'currentUser' },
        description: newPermission.description,
      };
      onChange([...permissions, permission]);
      setNewPermission({
        name: '',
        action: '',
        resource: '',
        condition: { createdBy: 'currentUser' },
        description: '',
      });
      setShowAddForm(false);
    }
  };

  const handleDeletePermission = (id: string) => {
    onChange(permissions.filter(p => p.id !== id));
  };

  const handleEditPermission = (permission: PolicyPermission) => {
    setEditingPermission(permission);
    setNewPermission(permission);
    setShowAddForm(true);
  };

  const handleUpdatePermission = () => {
    if (editingPermission && newPermission.name && newPermission.action && newPermission.resource) {
      const updatedPermissions = permissions.map(p =>
        p.id === editingPermission.id
          ? {
              ...p,
              name: newPermission.name!,
              action: newPermission.action!,
              resource: newPermission.resource!,
              condition: newPermission.condition || { createdBy: 'currentUser' },
              description: newPermission.description,
            }
          : p
      );
      onChange(updatedPermissions);
      setEditingPermission(null);
      setNewPermission({
        name: '',
        action: '',
        resource: '',
        condition: { createdBy: 'currentUser' },
        description: '',
      });
      setShowAddForm(false);
    }
  };

  const handleCancelEdit = () => {
    setEditingPermission(null);
    setNewPermission({
      name: '',
      action: '',
      resource: '',
      condition: { createdBy: 'currentUser' },
      description: '',
    });
    setShowAddForm(false);
  };

  const applyTemplate = (template: typeof PERMISSION_TEMPLATES[0]) => {
    setNewPermission(prev => ({
      ...prev,
      condition: template.condition as ButtonCondition,
    }));
  };

  const getConditionDisplay = (condition: ButtonCondition) => {
    if (condition.createdBy) {
      switch (condition.createdBy) {
        case 'currentUser':
          return 'Creator Only';
        case 'currentUserAndAbove':
          return 'Creator & Managers';
        case 'AnyUser':
          return 'Public Access';
        default:
          return condition.createdBy;
      }
    }
    if (condition.custom) {
      return 'Custom Rule';
    }
    return 'Unknown';
  };

  const getConditionColor = (condition: ButtonCondition) => {
    if (condition.createdBy) {
      switch (condition.createdBy) {
        case 'currentUser':
          return '#0969da';
        case 'currentUserAndAbove':
          return '#1f883d';
        case 'AnyUser':
          return '#8250df';
        default:
          return '#656d76';
      }
    }
    if (condition.custom) {
      return '#d1242f';
    }
    return '#656d76';
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#24292f' }}>
          Permissions
        </Typography>
        <Button
          size="small"
          variant="outlined"
          startIcon={<AddIcon />}
          onClick={() => setShowAddForm(true)}
          sx={{
            borderColor: '#d1d9e0',
            color: '#24292f',
            fontSize: '0.75rem',
            textTransform: 'none',
            '&:hover': {
              borderColor: '#0969da',
              backgroundColor: '#f6f8fa',
            },
          }}
        >
          Add Permission
        </Button>
      </Box>

      {/* Existing Permissions */}
      {permissions.length > 0 && (
        <Box mb={3}>
          <Grid container spacing={2}>
            {permissions.map((permission) => (
              <Grid item xs={12} key={permission.id}>
                <Card
                  sx={{
                    border: '1px solid #d1d9e0',
                    borderRadius: '6px',
                    boxShadow: 'none',
                    '&:hover': { borderColor: '#0969da' },
                  }}
                >
                  <CardContent sx={{ p: 2 }}>
                    <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                      <Box flex={1}>
                        <Box display="flex" alignItems="center" gap={1} mb={1}>
                          <Typography
                            variant="body2"
                            sx={{ fontWeight: 600, color: '#24292f' }}
                          >
                            {permission.name}
                          </Typography>
                          <Chip
                            label={getConditionDisplay(permission.condition)}
                            size="small"
                            sx={{
                              height: 18,
                              fontSize: '0.7rem',
                              backgroundColor: `${getConditionColor(permission.condition)}15`,
                              color: getConditionColor(permission.condition),
                            }}
                          />
                        </Box>
                        <Typography
                          variant="caption"
                          sx={{ color: '#656d76', display: 'block', mb: 1 }}
                        >
                          Action: <strong>{permission.action}</strong> • Resource: <strong>{permission.resource}</strong>
                        </Typography>
                        {permission.description && (
                          <Typography
                            variant="caption"
                            sx={{ color: '#656d76', fontSize: '0.75rem' }}
                          >
                            {permission.description}
                          </Typography>
                        )}
                      </Box>
                      <Box display="flex" gap={0.5}>
                        <Tooltip title="Edit permission">
                          <IconButton
                            size="small"
                            onClick={() => handleEditPermission(permission)}
                            sx={{ color: '#656d76', '&:hover': { color: '#0969da' } }}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete permission">
                          <IconButton
                            size="small"
                            onClick={() => handleDeletePermission(permission.id)}
                            sx={{ color: '#656d76', '&:hover': { color: '#da3633' } }}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}

      {/* Add/Edit Permission Form */}
      {showAddForm && (
        <Card
          sx={{
            border: '1px solid #0969da',
            borderRadius: '6px',
            boxShadow: 'none',
            mb: 2,
          }}
        >
          <CardContent sx={{ p: 3 }}>
            <Typography
              variant="subtitle2"
              sx={{ fontWeight: 600, color: '#24292f', mb: 2 }}
            >
              {editingPermission ? 'Edit Permission' : 'Add New Permission'}
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  size="small"
                  label="Permission Name"
                  value={newPermission.name || ''}
                  onChange={(e) => setNewPermission(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="e.g., Approve Loan"
                />
              </Grid>
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Action</InputLabel>
                  <Select
                    value={newPermission.action || ''}
                    onChange={(e) => setNewPermission(prev => ({ ...prev, action: e.target.value }))}
                    label="Action"
                  >
                    {COMMON_ACTIONS.map(action => (
                      <MenuItem key={action} value={action}>{action}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Resource</InputLabel>
                  <Select
                    value={newPermission.resource || ''}
                    onChange={(e) => setNewPermission(prev => ({ ...prev, resource: e.target.value }))}
                    label="Resource"
                  >
                    {COMMON_RESOURCES.map(resource => (
                      <MenuItem key={resource} value={resource}>{resource}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  size="small"
                  label="Description (Optional)"
                  value={newPermission.description || ''}
                  onChange={(e) => setNewPermission(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Brief description of this permission"
                />
              </Grid>
            </Grid>

            {/* Permission Templates */}
            <Box mt={3}>
              <Typography variant="caption" sx={{ color: '#656d76', mb: 1, display: 'block' }}>
                Quick Templates:
              </Typography>
              <Box display="flex" gap={1} flexWrap="wrap">
                {PERMISSION_TEMPLATES.map((template) => (
                  <Button
                    key={template.id}
                    size="small"
                    variant="outlined"
                    startIcon={
                      <Avatar sx={{ width: 16, height: 16, backgroundColor: template.color }}>
                        {React.cloneElement(template.icon, { sx: { fontSize: 10, color: 'white' } })}
                      </Avatar>
                    }
                    onClick={() => applyTemplate(template)}
                    sx={{
                      borderColor: '#d1d9e0',
                      color: '#24292f',
                      fontSize: '0.7rem',
                      textTransform: 'none',
                      '&:hover': { borderColor: template.color },
                    }}
                  >
                    {template.name}
                  </Button>
                ))}
              </Box>
            </Box>

            <Box display="flex" justifyContent="flex-end" gap={1} mt={3}>
              <Button
                size="small"
                onClick={handleCancelEdit}
                sx={{ color: '#656d76', textTransform: 'none' }}
              >
                Cancel
              </Button>
              <Button
                size="small"
                variant="contained"
                onClick={editingPermission ? handleUpdatePermission : handleAddPermission}
                sx={{
                  backgroundColor: '#1f883d',
                  color: 'white',
                  textTransform: 'none',
                  '&:hover': { backgroundColor: '#1a7f37' },
                }}
              >
                {editingPermission ? 'Update' : 'Add'} Permission
              </Button>
            </Box>
          </CardContent>
        </Card>
      )}

      {permissions.length === 0 && !showAddForm && (
        <Alert
          severity="info"
          sx={{
            borderRadius: '6px',
            backgroundColor: '#dbeafe',
            border: '1px solid #93c5fd',
            '& .MuiAlert-icon': { color: '#2563eb' },
            '& .MuiAlert-message': { color: '#1e40af' },
          }}
        >
          No permissions defined yet. Click "Add Permission" to get started.
        </Alert>
      )}
    </Box>
  );
};

export default InlinePermissionEditor;
