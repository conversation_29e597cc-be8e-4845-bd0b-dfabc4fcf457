import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment,
  Stack,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  Button,
  Divider,
} from '@mui/material';
import {
  CheckCircle as AllowIcon,
  Cancel as DenyIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  AccessTime as TimeIcon,
  Person as PersonIcon,
  Policy as PolicyIcon,
  Computer as SystemIcon,
} from '@mui/icons-material';

// Mock data
const mockDecisions = [
  {
    id: '1',
    timestamp: '2024-01-25T14:30:25Z',
    productName: 'Lender Dashboard',
    policyName: 'Loan Application Access',
    result: { allow: true, reason: 'User has valid lending permissions' },
    duration: 12,
    user: '<EMAIL>',
    requestId: 'req_1a2b3c4d',
    path: '/api/loans/applications',
    method: 'GET',
  },
  {
    id: '2',
    timestamp: '2024-01-25T14:29:18Z',
    productName: 'BankConnect',
    policyName: 'Account Verification',
    result: { allow: false, reason: 'Account verification failed' },
    duration: 8,
    user: '<EMAIL>',
    requestId: 'req_2e3f4g5h',
    path: '/api/accounts/verify',
    method: 'POST',
  },
  {
    id: '3',
    timestamp: '2024-01-25T14:28:45Z',
    productName: 'Sentinel',
    policyName: 'Fraud Detection',
    result: { allow: true, reason: 'Transaction passed fraud checks' },
    duration: 15,
    user: '<EMAIL>',
    requestId: 'req_3i4j5k6l',
    path: '/api/fraud/check',
    method: 'POST',
  },
  {
    id: '4',
    timestamp: '2024-01-25T14:27:32Z',
    productName: 'Lisa',
    policyName: 'Risk Assessment Access',
    result: { allow: false, reason: 'User lacks risk analyst role' },
    duration: 5,
    user: '<EMAIL>',
    requestId: 'req_4m5n6o7p',
    path: '/api/risk/assessment',
    method: 'GET',
  },
  {
    id: '5',
    timestamp: '2024-01-25T14:26:15Z',
    productName: 'Platform Dashboard',
    policyName: 'Admin Operations',
    result: { allow: true, reason: 'User has admin privileges' },
    duration: 22,
    user: '<EMAIL>',
    requestId: 'req_5q6r7s8t',
    path: '/api/admin/settings',
    method: 'PUT',
  },
  {
    id: '6',
    timestamp: '2024-01-25T14:25:03Z',
    productName: 'DeviceConnect',
    policyName: 'Device Registration',
    result: { allow: true, reason: 'Device meets security requirements' },
    duration: 18,
    user: '<EMAIL>',
    requestId: 'req_6u7v8w9x',
    path: '/api/devices/register',
    method: 'POST',
  },
];

const DecisionLogPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [productFilter, setProductFilter] = useState('');
  const [resultFilter, setResultFilter] = useState('');
  const [filteredDecisions, setFilteredDecisions] = useState(mockDecisions);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    applyFilters(query, productFilter, resultFilter);
  };

  const handleProductFilter = (product: string) => {
    setProductFilter(product);
    applyFilters(searchQuery, product, resultFilter);
  };

  const handleResultFilter = (result: string) => {
    setResultFilter(result);
    applyFilters(searchQuery, productFilter, result);
  };

  const applyFilters = (search: string, product: string, result: string) => {
    let filtered = mockDecisions;

    if (search) {
      filtered = filtered.filter(decision =>
        decision.user.toLowerCase().includes(search.toLowerCase()) ||
        decision.policyName.toLowerCase().includes(search.toLowerCase()) ||
        decision.result.reason.toLowerCase().includes(search.toLowerCase()) ||
        decision.productName.toLowerCase().includes(search.toLowerCase())
      );
    }

    if (product) {
      filtered = filtered.filter(decision => decision.productName === product);
    }

    if (result) {
      filtered = filtered.filter(decision =>
        result === 'allow' ? decision.result.allow : !decision.result.allow
      );
    }

    setFilteredDecisions(filtered);
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getRelativeTime = (timestamp: string) => {
    const now = new Date();
    const date = new Date(timestamp);
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) return 'just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return `${Math.floor(diffMins / 1440)}d ago`;
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header Section - GitHub Style */}
      <Box sx={{ mb: 4 }}>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
          <Box>
            <Typography
              variant="h4"
              component="h1"
              sx={{
                fontWeight: 600,
                fontSize: '2rem',
                color: '#24292f',
                mb: 1
              }}
            >
              History
            </Typography>
            <Typography
              variant="body1"
              sx={{
                color: '#656d76',
                fontSize: '1rem'
              }}
            >
              Monitor policy decisions and audit trail across your financial products
            </Typography>
          </Box>
          <Stack direction="row" spacing={1}>
            <Tooltip title="Refresh">
              <IconButton
                sx={{
                  color: '#656d76',
                  '&:hover': { color: '#0969da', backgroundColor: '#f6f8fa' }
                }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Export">
              <IconButton
                sx={{
                  color: '#656d76',
                  '&:hover': { color: '#0969da', backgroundColor: '#f6f8fa' }
                }}
              >
                <DownloadIcon />
              </IconButton>
            </Tooltip>
          </Stack>
        </Box>

        {/* Filters Bar - GitHub Style */}
        <Card
          sx={{
            border: '1px solid #d1d9e0',
            borderRadius: '6px',
            boxShadow: 'none',
            mb: 3
          }}
        >
          <CardContent sx={{ p: 3 }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  placeholder="Search decisions..."
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  size="small"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '6px',
                      backgroundColor: '#f6f8fa',
                      '&:hover': { backgroundColor: '#ffffff' },
                      '&.Mui-focused': { backgroundColor: '#ffffff' }
                    }
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon sx={{ color: '#656d76', fontSize: 20 }} />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Product</InputLabel>
                  <Select
                    label="Product"
                    value={productFilter}
                    onChange={(e) => handleProductFilter(e.target.value)}
                    sx={{
                      borderRadius: '6px',
                      backgroundColor: '#f6f8fa',
                      '&:hover': { backgroundColor: '#ffffff' },
                      '&.Mui-focused': { backgroundColor: '#ffffff' }
                    }}
                  >
                    <MenuItem value="">All Products</MenuItem>
                    <MenuItem value="Lender Dashboard">Lender Dashboard</MenuItem>
                    <MenuItem value="Platform Dashboard">Platform Dashboard</MenuItem>
                    <MenuItem value="Lisa">Lisa</MenuItem>
                    <MenuItem value="Sentinel">Sentinel</MenuItem>
                    <MenuItem value="BankConnect">BankConnect</MenuItem>
                    <MenuItem value="DeviceConnect">DeviceConnect</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Result</InputLabel>
                  <Select
                    label="Result"
                    value={resultFilter}
                    onChange={(e) => handleResultFilter(e.target.value)}
                    sx={{
                      borderRadius: '6px',
                      backgroundColor: '#f6f8fa',
                      '&:hover': { backgroundColor: '#ffffff' },
                      '&.Mui-focused': { backgroundColor: '#ffffff' }
                    }}
                  >
                    <MenuItem value="">All Results</MenuItem>
                    <MenuItem value="allow">Allow</MenuItem>
                    <MenuItem value="deny">Deny</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={2}>
                <Typography variant="body2" sx={{ color: '#656d76', textAlign: 'right' }}>
                  {filteredDecisions.length} {filteredDecisions.length === 1 ? 'decision' : 'decisions'}
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Box>

      {/* Decisions List - GitHub Style */}
      <Card
        sx={{
          border: '1px solid #d1d9e0',
          borderRadius: '6px',
          boxShadow: 'none',
          overflow: 'hidden'
        }}
      >
        {filteredDecisions.length > 0 ? (
          filteredDecisions.map((decision, index) => (
            <Box key={decision.id}>
              <Box
                sx={{
                  p: 3,
                  '&:hover': { backgroundColor: '#f6f8fa' },
                  transition: 'background-color 0.2s ease'
                }}
              >
                <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                  <Box display="flex" alignItems="flex-start" gap={2} flex={1}>
                    {/* Result Icon */}
                    <Box
                      sx={{
                        width: 32,
                        height: 32,
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: decision.result.allow ? '#dcfce7' : '#fef2f2',
                        color: decision.result.allow ? '#16a34a' : '#dc2626',
                        mt: 0.5
                      }}
                    >
                      {decision.result.allow ? <AllowIcon fontSize="small" /> : <DenyIcon fontSize="small" />}
                    </Box>

                    {/* Decision Info */}
                    <Box flex={1}>
                      <Box display="flex" alignItems="center" gap={1} mb={1}>
                        <Typography
                          variant="body1"
                          sx={{
                            fontWeight: 600,
                            color: '#24292f',
                            fontSize: '0.95rem'
                          }}
                        >
                          {decision.policyName}
                        </Typography>
                        <Chip
                          label={decision.result.allow ? 'ALLOW' : 'DENY'}
                          size="small"
                          sx={{
                            backgroundColor: decision.result.allow ? '#dcfce7' : '#fef2f2',
                            color: decision.result.allow ? '#16a34a' : '#dc2626',
                            fontWeight: 600,
                            fontSize: '0.7rem',
                            height: 20
                          }}
                        />
                      </Box>

                      <Typography
                        variant="body2"
                        sx={{ color: '#656d76', mb: 2, lineHeight: 1.5 }}
                      >
                        {decision.result.reason}
                      </Typography>

                      <Stack direction="row" spacing={3} alignItems="center" flexWrap="wrap">
                        <Box display="flex" alignItems="center" gap={0.5}>
                          <SystemIcon sx={{ fontSize: 16, color: '#656d76' }} />
                          <Typography variant="body2" sx={{ color: '#656d76', fontSize: '0.875rem' }}>
                            {decision.productName}
                          </Typography>
                        </Box>
                        <Box display="flex" alignItems="center" gap={0.5}>
                          <PersonIcon sx={{ fontSize: 16, color: '#656d76' }} />
                          <Typography variant="body2" sx={{ color: '#656d76', fontSize: '0.875rem' }}>
                            {decision.user}
                          </Typography>
                        </Box>
                        <Box display="flex" alignItems="center" gap={0.5}>
                          <TimeIcon sx={{ fontSize: 16, color: '#656d76' }} />
                          <Typography variant="body2" sx={{ color: '#656d76', fontSize: '0.875rem' }}>
                            {decision.duration}ms
                          </Typography>
                        </Box>
                        <Typography variant="body2" sx={{ color: '#656d76', fontSize: '0.875rem', fontFamily: 'monospace' }}>
                          {decision.method} {decision.path}
                        </Typography>
                      </Stack>
                    </Box>
                  </Box>

                  {/* Timestamp */}
                  <Box textAlign="right">
                    <Typography
                      variant="body2"
                      sx={{ color: '#656d76', fontSize: '0.875rem', fontFamily: 'monospace' }}
                    >
                      {formatTimestamp(decision.timestamp)}
                    </Typography>
                    <Typography
                      variant="caption"
                      sx={{ color: '#656d76', fontSize: '0.75rem' }}
                    >
                      {getRelativeTime(decision.timestamp)}
                    </Typography>
                  </Box>
                </Box>
              </Box>
              {index < filteredDecisions.length - 1 && <Divider />}
            </Box>
          ))
        ) : (
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            sx={{ py: 8 }}
          >
            <PolicyIcon sx={{ fontSize: 64, color: '#656d76', mb: 2 }} />
            <Typography variant="h6" sx={{ color: '#24292f', mb: 1 }}>
              No decisions found
            </Typography>
            <Typography variant="body2" sx={{ color: '#656d76', mb: 3 }}>
              {searchQuery || productFilter || resultFilter
                ? 'Try adjusting your search filters.'
                : 'Policy decisions will appear here as they are made.'}
            </Typography>
            {(searchQuery || productFilter || resultFilter) && (
              <Button
                variant="outlined"
                onClick={() => {
                  setSearchQuery('');
                  setProductFilter('');
                  setResultFilter('');
                  setFilteredDecisions(mockDecisions);
                }}
                sx={{
                  borderColor: '#d1d9e0',
                  color: '#656d76',
                  '&:hover': { borderColor: '#0969da', color: '#0969da' },
                  borderRadius: '6px',
                  textTransform: 'none',
                  fontWeight: 500
                }}
              >
                Clear filters
              </Button>
            )}
          </Box>
        )}
      </Card>
    </Box>
  );
};

export default DecisionLogPage;
