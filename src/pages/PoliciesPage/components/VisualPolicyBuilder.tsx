import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Alert,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Drawer,
  Divider,
  Chip,
} from '@mui/material';
import {
  Save as SaveIcon,
  ArrowBack as BackIcon,
  ArrowForward as ForwardIcon,
  Security as SecurityIcon,
  Settings as SettingsIcon,
  Preview as PreviewIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import { OSO_COLORS } from '@/styles/osoTheme';
import { PolicyOutput } from '@/types';
import { PolicyParser } from '@/lib/policyParser';

import GlobalSettingsStep from './PolicyBuilder/GlobalSettingsStep';
import PolicyPreviewStep from './PolicyBuilder/PolicyPreviewStep';
import ConsolidatedSetupStep from './PolicyBuilder/ConsolidatedSetupStep';
import SimpleCodePreview from './PolicyBuilder/SimpleCodePreview';

interface VisualPolicyBuilderProps {
  initialPolicy?: {
    name: string;
    description: string;
    content: string;
    productId?: string;
    permissions?: any[];
  };
  onSave?: (policyData: {
    name: string;
    description: string;
    content: string;
    productId?: string;
    permissions?: any[];
  }) => void;
  onCancel?: () => void;
}

interface PolicyFormData {
  name: string;
  description: string;
  productId: string;
  packageName: string;
  taskTypes: Record<string, any>;
  permissions: any[];
  globalSettings: {
    addConditionButton: boolean;
    allowUserTypeCheck: boolean;
    allowedUserTypes: string[];
  };
}

const steps = [
  {
    id: 'setup',
    label: 'Setup',
    icon: SecurityIcon,
    description: 'Product selection and policy configuration',
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: SettingsIcon,
    description: 'Global configuration and permissions',
  },
  {
    id: 'review',
    label: 'Review',
    icon: PreviewIcon,
    description: 'Preview and save policy',
  },
];

const SIDEBAR_WIDTH = 240;

const VisualPolicyBuilder: React.FC<VisualPolicyBuilderProps> = ({
  initialPolicy,
  onSave,
  onCancel,
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [splitRatio, setSplitRatio] = useState(50); // Percentage for main content (50% main, 50% code preview)
  const [isDragging, setIsDragging] = useState(false);
  const [formData, setFormData] = useState<PolicyFormData>({
    name: '',
    description: '',
    productId: '',
    packageName: 'authz.tasks',
    taskTypes: {},
    permissions: [],
    globalSettings: {
      addConditionButton: false,
      allowUserTypeCheck: true,
      allowedUserTypes: ['admin', 'user'],
    },
  });
  const [generatedPolicy, setGeneratedPolicy] = useState('');
  const [errors, setErrors] = useState<string[]>([]);

  // Handle resizable split-view
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return;

    const container = document.querySelector('[data-split-container]') as HTMLElement;
    if (!container) return;

    const containerRect = container.getBoundingClientRect();
    const newRatio = ((e.clientX - containerRect.left) / containerRect.width) * 100;

    // Constrain the ratio between 30% and 70% for better balance
    const constrainedRatio = Math.min(Math.max(newRatio, 30), 70);
    setSplitRatio(constrainedRatio);
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Add global mouse event listeners for dragging
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
    } else {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
  }, [isDragging]);

  // Initialize form data from existing policy
  useEffect(() => {
    if (initialPolicy) {
      try {
        const parsed = PolicyParser.parsePolicyContent(initialPolicy.content);
        setFormData({
          name: initialPolicy.name,
          description: initialPolicy.description,
          productId: initialPolicy.productId || '',
          packageName: 'policy', // Extract from content if needed
          taskTypes: parsed.output?.taskTypes || {},
          permissions: initialPolicy.permissions || [],
          globalSettings: {
            addConditionButton: parsed.output?.buttons?.addConditionButton || false,
            allowUserTypeCheck: true,
            allowedUserTypes: ['admin', 'user'],
          },
        });
      } catch (error) {
        console.warn('Failed to parse existing policy:', error);
      }
    }
  }, [initialPolicy]);

  // Generate policy content whenever form data changes
  useEffect(() => {
    try {
      const policy = generatePolicyFromForm(formData);
      setGeneratedPolicy(policy);
      setErrors([]);
    } catch (error) {
      setErrors([error instanceof Error ? error.message : 'Failed to generate policy']);
    }
  }, [formData]);

  const generatePolicyFromForm = (data: PolicyFormData): string => {
    const output: PolicyOutput = {
      taskTypes: data.taskTypes,
      buttons: {
        addConditionButton: data.globalSettings.addConditionButton,
      },
    };

    let policy = `package ${data.packageName}\n\n`;
    
    if (data.globalSettings.allowUserTypeCheck) {
      policy += `user_type_allowed(allowed_user_types) if input.user.type in allowed_user_types\n\n`;
      policy += `else := false\n\n`;
    }

    policy += PolicyParser.generateOutputString(output);

    return policy;
  };

  const handleNext = () => {
    const stepErrors = validateStep(activeStep);
    if (stepErrors.length === 0) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      setErrors([]);
    } else {
      setErrors(stepErrors);
    }
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
    setErrors([]);
  };

  const validateStep = (step: number): string[] => {
    const errors: string[] = [];

    switch (step) {
      case 0: // Setup (Basic Info + Task Types)
        if (!formData.name.trim()) {
          errors.push('Policy name is required');
        }
        if (!formData.productId.trim()) {
          errors.push('Financial product selection is required');
        }
        if (!formData.packageName.trim()) {
          errors.push('Package name is required');
        }
        if (Object.keys(formData.taskTypes).length === 0) {
          errors.push('At least one task type is required');
        }
        break;
      case 1: // Global Settings
        // No validation needed for global settings
        break;
    }

    return errors;
  };

  const getStepStatus = (stepIndex: number) => {
    if (stepIndex < activeStep) return 'completed';
    if (stepIndex === activeStep) return 'active';
    return 'pending';
  };

  const canNavigateToStep = (stepIndex: number) => {
    // Allow navigation to completed steps and the next step
    return stepIndex <= activeStep || stepIndex === activeStep + 1;
  };

  const handleSave = () => {
    try {
      // Validate all required fields
      const currentErrors = validateStep(0);
      if (currentErrors.length > 0) {
        setErrors(currentErrors);
        return;
      }

      if (onSave) {
        onSave({
          name: formData.name.trim(),
          description: formData.description?.trim(),
          content: generatedPolicy,
          productId: formData.productId,
          permissions: formData.permissions,
        });
      }
    } catch (error) {
      console.error('Error saving policy:', error);
      setErrors(['An unexpected error occurred while saving the policy. Please try again.']);
    }
  };

  const updateFormData = (updates: Partial<PolicyFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const updateBasicInfo = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <ConsolidatedSetupStep
            data={formData}
            onChange={updateFormData}
            onBasicInfoChange={updateBasicInfo}
            sidebarVisible={sidebarVisible}
            onToggleSidebar={() => setSidebarVisible(!sidebarVisible)}
          />
        );
      case 1:
        return (
          <GlobalSettingsStep
            data={formData}
            onChange={updateFormData}
          />
        );
      case 2:
        return (
          <PolicyPreviewStep
            data={formData}
            generatedPolicy={generatedPolicy}
          />
        );
      default:
        return null;
    }
  };

  const renderSidebar = () => (
    <Drawer
      variant="permanent"
      sx={{
        width: SIDEBAR_WIDTH,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: SIDEBAR_WIDTH,
          boxSizing: 'border-box',
          bgcolor: OSO_COLORS.surface,
          borderRight: `1px solid ${OSO_COLORS.border}`,
          position: 'relative',
        },
      }}
    >
      <Box sx={{ p: 2, borderBottom: `1px solid ${OSO_COLORS.border}` }}>
        <Typography variant="h6" fontWeight={600} color={OSO_COLORS.textPrimary}>
          Policy Builder
        </Typography>
        <Typography variant="body2" color="textSecondary">
          Create and configure policies
        </Typography>
      </Box>

      <List sx={{ px: 1, py: 2 }}>
        {steps.map((step, index) => {
          const status = getStepStatus(index);
          const Icon = step.icon;
          const isClickable = canNavigateToStep(index);

          return (
            <ListItem key={step.id} disablePadding sx={{ mb: 0.5 }}>
              <ListItemButton
                onClick={() => isClickable && setActiveStep(index)}
                disabled={!isClickable}
                sx={{
                  borderRadius: 1,
                  py: 1.5,
                  px: 2,
                  bgcolor: status === 'active' ? OSO_COLORS.primary + '10' : 'transparent',
                  border: status === 'active' ? `1px solid ${OSO_COLORS.primary}` : '1px solid transparent',
                  '&:hover': {
                    bgcolor: status === 'active' ? OSO_COLORS.primary + '15' : OSO_COLORS.surface,
                  },
                  '&.Mui-disabled': {
                    opacity: 0.5,
                  },
                }}
              >
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: 24,
                      height: 24,
                      borderRadius: '50%',
                      bgcolor: status === 'completed' ? OSO_COLORS.success :
                              status === 'active' ? OSO_COLORS.primary : OSO_COLORS.border,
                      color: status === 'pending' ? OSO_COLORS.textSecondary : 'white',
                    }}
                  >
                    {status === 'completed' ? (
                      <CheckCircleIcon sx={{ fontSize: 16 }} />
                    ) : (
                      <Icon sx={{ fontSize: 14 }} />
                    )}
                  </Box>
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Typography
                      variant="body2"
                      fontWeight={status === 'active' ? 600 : 500}
                      color={status === 'active' ? OSO_COLORS.primary : OSO_COLORS.textPrimary}
                    >
                      {step.label}
                    </Typography>
                  }
                  secondary={
                    <Typography variant="caption" color="textSecondary">
                      {step.description}
                    </Typography>
                  }
                />
                {status === 'completed' && (
                  <Chip
                    size="small"
                    label="✓"
                    sx={{
                      bgcolor: OSO_COLORS.success,
                      color: 'white',
                      fontSize: '0.7rem',
                      height: 20,
                      minWidth: 20,
                    }}
                  />
                )}
              </ListItemButton>
            </ListItem>
          );
        })}
      </List>

      <Divider />

      <Box sx={{ p: 2, mt: 'auto' }}>
        <Typography variant="caption" color="textSecondary" display="block" gutterBottom>
          Progress: {activeStep + 1} of {steps.length}
        </Typography>
        <Box
          sx={{
            width: '100%',
            height: 4,
            bgcolor: OSO_COLORS.border,
            borderRadius: 2,
            overflow: 'hidden',
          }}
        >
          <Box
            sx={{
              width: `${((activeStep + 1) / steps.length) * 100}%`,
              height: '100%',
              bgcolor: OSO_COLORS.primary,
              transition: 'width 0.3s ease',
            }}
          />
        </Box>
      </Box>
    </Drawer>
  );

  return (
    <Box sx={{
      minHeight: '100vh',
      bgcolor: OSO_COLORS.background,
      display: 'flex',
    }}>
      {/* Vertical Sidebar Navigation */}
      {renderSidebar()}

      {/* Main Content Area */}
      <Box sx={{
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
      }}>

        {/* Compact Header */}
        <Box sx={{
          px: 3,
          py: 1.5,
          borderBottom: `1px solid ${OSO_COLORS.border}`,
          bgcolor: 'white',
        }}>
          <Typography variant="h6" fontWeight={600} color={OSO_COLORS.textPrimary}>
            {steps[activeStep].label}
          </Typography>
          <Typography variant="body2" color="textSecondary" sx={{ fontSize: '0.8rem' }}>
            {steps[activeStep].description}
          </Typography>
        </Box>

        {/* Content Area - Split Layout for Setup Step */}
        <Box
          data-split-container
          sx={{
            flexGrow: 1,
            overflow: 'hidden',
            bgcolor: OSO_COLORS.background,
            display: 'flex',
            position: 'relative',
          }}
        >
          {/* Main Content */}
          <Box sx={{
            width: (activeStep === 0 && sidebarVisible) ? `${splitRatio}%` : '100%',
            overflow: 'auto',
            transition: isDragging ? 'none' : 'width 0.3s ease-in-out',
            position: 'relative',
            height: '100%',
          }}>
            {errors.length > 0 && (
              <Alert
                severity="error"
                sx={{
                  m: 2,
                  borderRadius: 1,
                  fontSize: '0.875rem',
                  py: 0.5,
                }}
              >
                <ul style={{ margin: 0, paddingLeft: 16 }}>
                  {errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </Alert>
            )}

            <Box sx={{
              p: 2,
              minHeight: 'fit-content',
              position: 'relative',
            }}>
              {renderStepContent(activeStep)}
            </Box>
          </Box>

          {/* Draggable Divider - Only for Setup Step */}
          {activeStep === 0 && sidebarVisible && (
            <Box
              onMouseDown={handleMouseDown}
              sx={{
                width: '4px',
                backgroundColor: OSO_COLORS.border,
                cursor: 'col-resize',
                position: 'relative',
                flexShrink: 0,
                '&:hover': {
                  backgroundColor: '#0366d6',
                },
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: '-2px',
                  right: '-2px',
                  bottom: 0,
                  backgroundColor: 'transparent',
                },
              }}
            />
          )}

          {/* Simple Code Preview - Only for Setup Step */}
          {activeStep === 0 && sidebarVisible && (
            <Box sx={{
              width: `${100 - splitRatio}%`,
              overflow: 'hidden',
              transition: isDragging ? 'none' : 'width 0.3s ease-in-out',
              p: 2,
            }}>
              <SimpleCodePreview
                taskTypes={formData.taskTypes}
                globalSettings={formData.globalSettings}
                packageName={formData.packageName}
              />
            </Box>
          )}
        </Box>

        {/* Compact Footer Actions */}
        <Box sx={{
          px: 3,
          py: 1.5,
          borderTop: `1px solid ${OSO_COLORS.border}`,
          bgcolor: 'white',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>
          <Button
            onClick={onCancel}
            variant="text"
            size="small"
            sx={{
              color: OSO_COLORS.textSecondary,
              fontSize: '0.75rem',
              px: 1.5,
              py: 0.25,
              minWidth: 'auto',
              textTransform: 'none',
              '&:hover': {
                backgroundColor: `${OSO_COLORS.border}20`,
              },
            }}
          >
            Cancel
          </Button>

          <Box display="flex" gap={0.5}>
            <Button
              disabled={activeStep === 0}
              onClick={handleBack}
              startIcon={<BackIcon sx={{ fontSize: '0.875rem' }} />}
              variant="outlined"
              size="small"
              sx={{
                fontSize: '0.75rem',
                px: 1.5,
                py: 0.25,
                minWidth: 'auto',
                textTransform: 'none',
                borderColor: OSO_COLORS.border,
                color: OSO_COLORS.textSecondary,
              }}
            >
              Back
            </Button>

            {activeStep === steps.length - 1 ? (
              <Button
                variant="contained"
                onClick={handleSave}
                startIcon={<SaveIcon sx={{ fontSize: '0.875rem' }} />}
                disabled={errors.length > 0}
                size="small"
                sx={{
                  bgcolor: OSO_COLORS.primary,
                  fontSize: '0.75rem',
                  px: 1.5,
                  py: 0.25,
                  minWidth: 'auto',
                  textTransform: 'none',
                  boxShadow: 'none',
                  '&:hover': {
                    boxShadow: 'none',
                  },
                }}
              >
                Save
              </Button>
            ) : (
              <Button
                variant="contained"
                onClick={handleNext}
                endIcon={<ForwardIcon sx={{ fontSize: '0.875rem' }} />}
                size="small"
                sx={{
                  bgcolor: OSO_COLORS.primary,
                  fontSize: '0.75rem',
                  px: 1.5,
                  py: 0.25,
                  minWidth: 'auto',
                  textTransform: 'none',
                  boxShadow: 'none',
                  '&:hover': {
                    boxShadow: 'none',
                  },
                }}
              >
                Next
              </Button>
            )}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default VisualPolicyBuilder;
