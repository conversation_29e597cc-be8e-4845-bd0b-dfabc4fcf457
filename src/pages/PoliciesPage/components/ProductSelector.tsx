import React from 'react';
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  Typography,
  Chip,
  Alert,
  SelectChangeEvent,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Security as SecurityIcon,
  AccountBalance as BankIcon,
  DeviceHub as DeviceIcon,
} from '@mui/icons-material';
import { POLICY_MANAGEMENT_PRODUCTS } from '@/constants';


interface ProductSelectorProps {
  value: string;
  onChange: (productId: string) => void;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  helperText?: string;
}

const getProductIcon = (iconType: string) => {
  switch (iconType) {
    case 'dashboard':
      return <DashboardIcon />;
    case 'security':
      return <SecurityIcon />;
    case 'bank':
      return <BankIcon />;
    case 'device':
      return <DeviceIcon />;
    default:
      return <DashboardIcon />;
  }
};

const ProductSelector: React.FC<ProductSelectorProps> = ({
  value,
  onChange,
  error,
  required = true,
  disabled = false,
  helperText,
}) => {
  const handleChange = (event: SelectChangeEvent<string>) => {
    onChange(event.target.value);
  };

  const selectedProduct = POLICY_MANAGEMENT_PRODUCTS.find(product => product.id === value);

  return (
    <Box>
      <FormControl 
        fullWidth 
        error={!!error}
        disabled={disabled}
        sx={{
          '& .MuiOutlinedInput-root': {
            borderRadius: '6px',
            backgroundColor: '#f6f8fa',
            '&:hover': { backgroundColor: '#ffffff' },
            '&.Mui-focused': { backgroundColor: '#ffffff' },
            '&.Mui-error': {
              borderColor: '#da3633',
              '&:hover': { borderColor: '#da3633' },
            },
          },
          '& .MuiInputLabel-root': {
            color: '#24292f',
            fontWeight: 500,
            '&.Mui-focused': { color: '#0969da' },
            '&.Mui-error': { color: '#da3633' },
          },
        }}
      >
        <InputLabel id="product-selector-label">
          Product {required && '*'}
        </InputLabel>
        <Select
          labelId="product-selector-label"
          value={value}
          label={`Product ${required ? '*' : ''}`}
          onChange={handleChange}
          renderValue={(selected) => {
            if (!selected) return '';
            const product = POLICY_MANAGEMENT_PRODUCTS.find(p => p.id === selected);
            if (!product) return selected;
            
            return (
              <Box display="flex" alignItems="center" gap={1.5}>
                <Avatar
                  sx={{
                    width: 24,
                    height: 24,
                    backgroundColor: product.color,
                    color: 'white',
                  }}
                >
                  {React.cloneElement(getProductIcon(product.icon), { 
                    sx: { fontSize: 14 } 
                  })}
                </Avatar>
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  {product.name}
                </Typography>
                <Chip
                  label={product.type.toUpperCase()}
                  size="small"
                  sx={{
                    height: 18,
                    fontSize: '0.7rem',
                    fontWeight: 600,
                    backgroundColor: product.color,
                    color: 'white',
                  }}
                />
              </Box>
            );
          }}
        >
          {POLICY_MANAGEMENT_PRODUCTS.map((product) => (
            <MenuItem 
              key={product.id} 
              value={product.id}
              sx={{
                py: 1.5,
                '&:hover': { backgroundColor: '#f6f8fa' },
                '&.Mui-selected': { 
                  backgroundColor: '#e7f3ff',
                  '&:hover': { backgroundColor: '#d0e9ff' },
                },
              }}
            >
              <Box display="flex" alignItems="center" gap={2} width="100%">
                <Avatar
                  sx={{
                    width: 32,
                    height: 32,
                    backgroundColor: product.color,
                    color: 'white',
                  }}
                >
                  {getProductIcon(product.icon)}
                </Avatar>
                <Box flex={1}>
                  <Box display="flex" alignItems="center" gap={1} mb={0.5}>
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        fontWeight: 600,
                        color: '#24292f',
                      }}
                    >
                      {product.name}
                    </Typography>
                    <Chip
                      label={product.type.toUpperCase()}
                      size="small"
                      sx={{
                        height: 16,
                        fontSize: '0.65rem',
                        fontWeight: 600,
                        backgroundColor: product.color,
                        color: 'white',
                      }}
                    />
                  </Box>
                  <Typography 
                    variant="caption" 
                    sx={{ 
                      color: '#656d76',
                      fontSize: '0.75rem',
                      lineHeight: 1.2,
                    }}
                  >
                    {product.description}
                  </Typography>
                </Box>
              </Box>
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {error && (
        <Alert 
          severity="error" 
          sx={{ 
            mt: 1,
            borderRadius: '6px',
            backgroundColor: '#fef2f2',
            border: '1px solid #fecaca',
            '& .MuiAlert-icon': { color: '#da3633' },
            '& .MuiAlert-message': { color: '#991b1b', fontSize: '0.875rem' },
          }}
        >
          {error}
        </Alert>
      )}

      {helperText && !error && (
        <Typography 
          variant="caption" 
          sx={{ 
            color: '#656d76',
            fontSize: '0.75rem',
            mt: 0.5,
            display: 'block',
          }}
        >
          {helperText}
        </Typography>
      )}


    </Box>
  );
};

export default ProductSelector;
