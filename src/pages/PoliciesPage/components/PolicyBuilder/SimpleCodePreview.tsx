import React, { useState, useEffect } from 'react';
import {
  Box,
  Tooltip,
  Button,
  Snackbar,
  Alert,
} from '@mui/material';
import {
  ContentCopy as CopyIcon,
} from '@mui/icons-material';

import { PolicyParser } from '@/lib/policyParser';
import { PolicyOutput } from '@/types';

interface SimpleCodePreviewProps {
  taskTypes: Record<string, any>;
  globalSettings: any;
  packageName: string;
}

const SimpleCodePreview: React.FC<SimpleCodePreviewProps> = ({
  taskTypes,
  globalSettings,
  packageName,
}) => {
  const [generatedCode, setGeneratedCode] = useState<string>('# Loading...');
  const [copySuccess, setCopySuccess] = useState(false);
  const [codeTheme, setCodeTheme] = useState<'light' | 'dark'>('light');

  // Copy to clipboard functionality
  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(generatedCode);
      setCopySuccess(true);
    } catch (err) {
      console.error('Failed to copy code:', err);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = generatedCode;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopySuccess(true);
    }
  };



  // Close copy success snackbar
  const handleCloseCopySnackbar = () => {
    setCopySuccess(false);
  };

  useEffect(() => {
    generateOpaCode();
  }, [taskTypes, globalSettings, packageName]);

  const generateOpaCode = () => {
    try {
      if (!taskTypes || Object.keys(taskTypes).length === 0) {
        setGeneratedCode(`# No task types configured yet
# Add task types and permissions to see the generated policy code

package ${packageName || 'authz.tasks'}

# This policy will be generated based on your configuration
default allow = false`);
        return;
      }

      // Create a PolicyOutput structure from the task types
      const policyOutput: PolicyOutput = {
        taskTypes,
        buttons: {
          addConditionButton: globalSettings?.addConditionButton || false,
        },
      };

      // Generate the complete policy
      const basePolicy = `package ${packageName || 'authz.tasks'}

default allow = false

# Allow access based on task type and status permissions
allow {
    input.resource.type == "task"
    task_type := input.resource.task_type
    status := input.resource.status
    button := input.action

    # Check if the task type exists in our configuration
    output.taskTypes[task_type]

    # Check if the status exists for this task type
    output.taskTypes[task_type].status[status]

    # Check if the button exists for this status
    button_config := output.taskTypes[task_type].status[status].buttons[button]

    # Evaluate the button condition
    evaluate_condition(button_config.condition)
}

# Helper function to evaluate conditions
evaluate_condition(condition) {
    condition.createdBy == "AnyUser"
}

evaluate_condition(condition) {
    condition.createdBy == "currentUser"
    input.user.id == input.resource.created_by
}

evaluate_condition(condition) {
    condition.createdBy == "currentUserAndAbove"
    # Add your hierarchy logic here
    input.user.id == input.resource.created_by
}

# Custom condition evaluation
evaluate_condition(condition) {
    condition.custom
    # Custom Rego code would be evaluated here
}

`;

      const policy = PolicyParser.generatePolicyContent(basePolicy, policyOutput);

      setGeneratedCode(policy);
    } catch (error) {
      console.error('Error generating OPA code:', error);
      setGeneratedCode(`# Error generating policy code
# Please check your configuration

package ${packageName || 'authz.tasks'}

default allow = false`);
    }
  };

  // Get theme-specific colors
  const getThemeColors = () => {
    if (codeTheme === 'dark') {
      return {
        background: '#1e1e1e',
        text: '#d4d4d4',
        lineNumber: '#858585',
        keyword: '#569cd6',
        string: '#ce9178',
        comment: '#6a9955',
        operator: '#d4d4d4',
        border: '#3e3e3e',
      };
    } else {
      return {
        background: '#ffffff',
        text: '#24292e',
        lineNumber: '#6e7681',
        keyword: '#d73a49',
        string: '#032f62',
        comment: '#6a737d',
        operator: '#24292e',
        border: '#e1e4e8',
      };
    }
  };

  const themeColors = getThemeColors();

  const renderCodeWithLineNumbers = (code: string): React.ReactNode => {
    const lines = code.split('\n');

    return lines.map((line, lineIndex) => (
      <Box
        key={lineIndex}
        component="div"
        sx={{
          display: 'flex',
          fontSize: '0.875rem',
          fontFamily: 'SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace',
          lineHeight: 1.6,
          minHeight: '1.6em',
          '&:hover': {
            backgroundColor: codeTheme === 'dark' ? '#2d2d30' : '#f6f8fa',
          },
        }}
      >
        {/* Line number */}
        <Box
          component="span"
          sx={{
            display: 'inline-block',
            width: '3em',
            textAlign: 'right',
            paddingRight: '1em',
            color: themeColors.lineNumber,
            userSelect: 'none',
            borderRight: `1px solid ${themeColors.border}`,
            marginRight: '1em',
            flexShrink: 0,
          }}
        >
          {lineIndex + 1}
        </Box>

        {/* Code content */}
        <Box
          component="span"
          sx={{
            color: themeColors.text,
            whiteSpace: 'pre',
            flex: 1,
            paddingRight: '1em',
          }}
        >
          {line || ' '}
        </Box>
      </Box>
    ));
  };

  return (
    <Box sx={{
      height: '100%',
      overflow: 'hidden',
      display: 'flex',
      flexDirection: 'column',
    }}>
      {/* Minimal controls overlay */}
      <Box sx={{
        position: 'absolute',
        top: 8,
        right: 8,
        zIndex: 1,
        display: 'flex',
        gap: 0.5,
        opacity: 0.7,
        '&:hover': { opacity: 1 },
      }}>
        {/* Copy Button */}
        <Tooltip title="Copy code to clipboard">
          <Button
            variant="outlined"
            size="small"
            startIcon={<CopyIcon />}
            onClick={handleCopyCode}
            sx={{
              textTransform: 'none',
              fontSize: '0.7rem',
              minWidth: 'auto',
              padding: '2px 6px',
              borderColor: themeColors.border,
              color: themeColors.text,
              backgroundColor: `${themeColors.background}dd`,
              backdropFilter: 'blur(4px)',
              '&:hover': {
                borderColor: codeTheme === 'dark' ? '#0e639c' : '#0366d6',
                backgroundColor: `${themeColors.background}ee`,
              },
            }}
          >
            Copy
          </Button>
        </Tooltip>
      </Box>

      {/* Code content */}
      <Box sx={{
        flex: 1,
        overflow: 'auto',
        backgroundColor: themeColors.background,
        '&::-webkit-scrollbar': {
          width: 8,
          height: 8,
        },
        '&::-webkit-scrollbar-track': {
          backgroundColor: codeTheme === 'dark' ? '#2d2d30' : '#f6f8fa',
        },
        '&::-webkit-scrollbar-thumb': {
          backgroundColor: codeTheme === 'dark' ? '#555' : '#d1d5da',
          borderRadius: 4,
          '&:hover': {
            backgroundColor: codeTheme === 'dark' ? '#777' : '#c6cbd1',
          },
        },
      }}>
        {generatedCode ? renderCodeWithLineNumbers(generatedCode) : (
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: themeColors.text,
            fontStyle: 'italic',
            fontSize: '0.875rem',
          }}>
            No code generated yet...
          </Box>
        )}
      </Box>

      {/* Copy success snackbar */}
      <Snackbar
        open={copySuccess}
        autoHideDuration={2000}
        onClose={handleCloseCopySnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseCopySnackbar}
          severity="success"
          variant="filled"
          sx={{ fontSize: '0.875rem' }}
        >
          Code copied to clipboard!
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default SimpleCodePreview;
