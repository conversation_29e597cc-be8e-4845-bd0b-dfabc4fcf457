import { api } from '@/lib/api';
import { Policy, PolicyVersion, CreatePolicyForm, PaginatedResponse, PolicyFilters } from '@/types';
import { API_ENDPOINTS } from '@/constants';

export interface PoliciesQueryParams extends PolicyFilters {
  page?: number;
  limit?: number;
}

export interface PolicyTestRequest {
  content: string;
  input: Record<string, any>;
}

export interface PolicyTestResponse {
  result: {
    allow: boolean;
    reason?: string;
    metadata?: Record<string, any>;
  };
  duration: number;
  errors?: string[];
}

export interface PolicyValidationResponse {
  valid: boolean;
  errors?: string[];
  warnings?: string[];
}

export const policiesApi = {
  // Get all policies with optional filtering and pagination
  getPolicies: async (params?: PoliciesQueryParams): Promise<PaginatedResponse<Policy>> => {
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.systemId) queryParams.append('systemId', params.systemId);
    if (params?.status) queryParams.append('status', params.status);
    if (params?.tags) params.tags.forEach(tag => queryParams.append('tags', tag));
    
    const url = `${API_ENDPOINTS.POLICIES}?${queryParams.toString()}`;
    return api.get<PaginatedResponse<Policy>>(url);
  },

  // Get policy by ID
  getPolicyById: async (id: string): Promise<Policy> => {
    return api.get<Policy>(API_ENDPOINTS.POLICY_BY_ID(id));
  },

  // Create new policy
  createPolicy: async (data: CreatePolicyForm): Promise<Policy> => {
    return api.post<Policy>(API_ENDPOINTS.POLICIES, data);
  },

  // Update policy
  updatePolicy: async (id: string, data: Partial<CreatePolicyForm>): Promise<Policy> => {
    return api.put<Policy>(API_ENDPOINTS.POLICY_BY_ID(id), data);
  },

  // Delete policy
  deletePolicy: async (id: string): Promise<void> => {
    return api.delete<void>(API_ENDPOINTS.POLICY_BY_ID(id));
  },

  // Validate policy content
  validatePolicy: async (content: string): Promise<PolicyValidationResponse> => {
    return api.post<PolicyValidationResponse>(API_ENDPOINTS.POLICY_VALIDATE, { content });
  },

  // Test policy with input data
  testPolicy: async (request: PolicyTestRequest): Promise<PolicyTestResponse> => {
    return api.post<PolicyTestResponse>(API_ENDPOINTS.POLICY_TEST, request);
  },

  // Get policy versions
  getPolicyVersions: async (id: string): Promise<PolicyVersion[]> => {
    return api.get<PolicyVersion[]>(API_ENDPOINTS.POLICY_VERSIONS(id));
  },

  // Deploy policy (activate a specific version)
  deployPolicy: async (id: string, version: string): Promise<Policy> => {
    return api.post<Policy>(`${API_ENDPOINTS.POLICY_BY_ID(id)}/deploy`, { version });
  },

  // Get policies by product
  getPoliciesByProduct: async (productId: string): Promise<Policy[]> => {
    return api.get<Policy[]>(`${API_ENDPOINTS.PRODUCTS}/${productId}/policies`);
  },
};
