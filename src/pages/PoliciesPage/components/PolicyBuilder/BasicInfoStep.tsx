import React from 'react';
import {
  <PERSON>,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
} from '@mui/material';
import { OSO_COLORS } from '@/styles/osoTheme';

interface BasicInfoStepProps {
  data: {
    name: string;
    packageName: string;
    description: string;
  };
  onChange: (field: string, value: string) => void;
}

const packageTemplates = [
  { value: 'authz.tasks', label: 'authz.tasks', description: 'Standard task authorization' },
  { value: 'authz.resources', label: 'authz.resources', description: 'Resource-based authorization' },
  { value: 'authz.users', label: 'authz.users', description: 'User-based authorization' },
  { value: 'authz.custom', label: 'authz.custom', description: 'Custom authorization logic' },
];

const BasicInfoStep: React.FC<BasicInfoStepProps> = ({ data, onChange }) => {
  const handleChange = (field: string, value: string) => {
    onChange(field, value);
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ mb: 2, pb: 2, borderBottom: `1px solid ${OSO_COLORS.border}` }}>
        <Typography 
          variant="h6" 
          sx={{ 
            fontWeight: 600,
            color: OSO_COLORS.textPrimary,
            mb: 0.5,
            fontSize: '1.125rem',
          }}
        >
          Basic Policy Information
        </Typography>
        <Typography 
          variant="body2" 
          sx={{ 
            color: OSO_COLORS.textSecondary,
            fontSize: '0.875rem',
          }}
        >
          Provide the basic details for your authorization policy.
        </Typography>
      </Box>

      {/* Two Column Layout */}
      <Box sx={{ flexGrow: 1, display: 'flex', gap: 4 }}>
        {/* Left Column - Form Fields */}
        <Box sx={{ flex: 2 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            {/* Policy Name and Package */}
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Box sx={{ flex: 2 }}>
                <TextField
                  fullWidth
                  label="Policy Name"
                  value={data.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                  placeholder="e.g., User Access Control"
                  required
                  variant="outlined"
                  size="small"
                  sx={{
                    '& .MuiInputLabel-root': { fontSize: '0.875rem' },
                    '& .MuiOutlinedInput-root': { fontSize: '0.875rem', borderRadius: 1 },
                  }}
                />
              </Box>
              <Box sx={{ flex: 1 }}>
                <FormControl fullWidth required size="small">
                  <InputLabel sx={{ fontSize: '0.875rem' }}>Package</InputLabel>
                  <Select
                    value={data.packageName}
                    onChange={(e) => handleChange('packageName', e.target.value)}
                    label="Package"
                    sx={{ fontSize: '0.875rem', borderRadius: 1 }}
                  >
                    {packageTemplates.map((template) => (
                      <MenuItem key={template.value} value={template.value}>
                        <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                          {template.label}
                        </Typography>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
            </Box>

            {/* Description */}
            <Box>
              <TextField
                fullWidth
                label="Description (Optional)"
                value={data.description}
                onChange={(e) => handleChange('description', e.target.value)}
                placeholder="Brief description of what this policy controls..."
                multiline
                rows={3}
                variant="outlined"
                size="small"
                sx={{
                  '& .MuiInputLabel-root': { fontSize: '0.875rem' },
                  '& .MuiOutlinedInput-root': { fontSize: '0.875rem', borderRadius: 1 },
                }}
              />
            </Box>
          </Box>
        </Box>

        {/* Right Column - Sidebar */}
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', gap: 2 }}>
          {/* Policy Features */}
          <Box 
            sx={{
              p: 2,
              bgcolor: OSO_COLORS.surface,
              border: `1px solid ${OSO_COLORS.border}`,
              borderRadius: 1,
            }}
          >
            <Typography 
              variant="subtitle2" 
              sx={{ 
                fontWeight: 600,
                color: OSO_COLORS.textPrimary,
                fontSize: '0.875rem',
                mb: 1.5,
              }}
            >
              Policy Features
            </Typography>
            <Box display="flex" flexDirection="column" gap={1}>
              <Chip
                label="Task-based Authorization"
                sx={{
                  bgcolor: `${OSO_COLORS.primary}15`,
                  color: OSO_COLORS.primary,
                  fontWeight: 500,
                  fontSize: '0.75rem',
                  height: 24,
                  justifyContent: 'flex-start',
                  borderRadius: 1,
                }}
                size="small"
              />
              <Chip
                label="Status-based Permissions"
                sx={{
                  bgcolor: `${OSO_COLORS.accent}15`,
                  color: OSO_COLORS.accent,
                  fontWeight: 500,
                  fontSize: '0.75rem',
                  height: 24,
                  justifyContent: 'flex-start',
                  borderRadius: 1,
                }}
                size="small"
              />
              <Chip
                label="Button-level Control"
                sx={{
                  bgcolor: `${OSO_COLORS.success}15`,
                  color: OSO_COLORS.success,
                  fontWeight: 500,
                  fontSize: '0.75rem',
                  height: 24,
                  justifyContent: 'flex-start',
                  borderRadius: 1,
                }}
                size="small"
              />
              <Chip
                label="User Type Validation"
                sx={{
                  bgcolor: `${OSO_COLORS.warning}15`,
                  color: OSO_COLORS.warning,
                  fontWeight: 500,
                  fontSize: '0.75rem',
                  height: 24,
                  justifyContent: 'flex-start',
                  borderRadius: 1,
                }}
                size="small"
              />
            </Box>
          </Box>

          {/* Preview */}
          <Box 
            sx={{
              p: 2,
              bgcolor: OSO_COLORS.surface,
              border: `1px solid ${OSO_COLORS.border}`,
              borderRadius: 1,
              flexGrow: 1,
            }}
          >
            <Typography 
              variant="subtitle2" 
              sx={{ 
                fontWeight: 600, 
                color: OSO_COLORS.textPrimary, 
                fontSize: '0.875rem',
                mb: 1.5,
              }}
            >
              Preview
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Box>
                <Typography variant="caption" sx={{ fontWeight: 600, fontSize: '0.75rem' }}>
                  Name:
                </Typography>
                <Typography variant="body2" sx={{ fontSize: '0.875rem', color: OSO_COLORS.textSecondary }}>
                  {data.name || 'Untitled Policy'}
                </Typography>
              </Box>
              <Box>
                <Typography variant="caption" sx={{ fontWeight: 600, fontSize: '0.75rem' }}>
                  Package:
                </Typography>
                <Typography variant="body2" sx={{ fontSize: '0.875rem', color: OSO_COLORS.textSecondary }}>
                  {data.packageName}
                </Typography>
              </Box>
              {data.description && (
                <Box>
                  <Typography variant="caption" sx={{ fontWeight: 600, fontSize: '0.75rem' }}>
                    Description:
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.875rem', color: OSO_COLORS.textSecondary }}>
                    {data.description}
                  </Typography>
                </Box>
              )}
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default BasicInfoStep;
