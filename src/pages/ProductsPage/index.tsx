import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Paper,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  Stack,
  Avatar,
  Divider,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Search as SearchIcon,
  Computer as ComputerIcon,
  Api as ApiIcon,
  Web as WebIcon,
  Storage as ServiceIcon,
  Circle as CircleIcon,
  Dashboard as DashboardIcon,
  AccountBalance as BankIcon,
  Security as SecurityIcon,
  DeviceHub as DeviceIcon,
} from '@mui/icons-material';

// Financial Products Mock Data
const mockProducts = [
  {
    id: '1',
    name: 'Lender Dashboard',
    type: 'web',
    status: 'active',
    url: 'https://lender.fintech.com',
    createdAt: '2024-01-15',
    description: 'Comprehensive lending management platform for financial institutions',
    policies: 8,
    lastActivity: '5 minutes ago',
  },
  {
    id: '2',
    name: 'Platform Dashboard',
    type: 'web',
    status: 'active',
    url: 'https://platform.fintech.com',
    createdAt: '2024-01-10',
    description: 'Central platform for managing all financial services and operations',
    policies: 12,
    lastActivity: '2 minutes ago',
  },
  {
    id: '3',
    name: 'Lisa',
    type: 'api',
    status: 'active',
    url: 'https://api.lisa.fintech.com',
    createdAt: '2024-01-20',
    description: 'AI-powered lending intelligence and risk assessment API',
    policies: 6,
    lastActivity: '1 minute ago',
  },
  {
    id: '4',
    name: 'Sentinel',
    type: 'service',
    status: 'active',
    url: 'https://sentinel.fintech.com',
    createdAt: '2024-01-05',
    description: 'Real-time fraud detection and security monitoring service',
    policies: 15,
    lastActivity: '30 seconds ago',
  },
  {
    id: '5',
    name: 'BankConnect',
    type: 'api',
    status: 'maintenance',
    url: 'https://api.bankconnect.fintech.com',
    createdAt: '2024-01-12',
    description: 'Secure banking integration and account connectivity API',
    policies: 9,
    lastActivity: '2 hours ago',
  },
  {
    id: '6',
    name: 'DeviceConnect',
    type: 'service',
    status: 'active',
    url: 'https://device.fintech.com',
    createdAt: '2024-01-18',
    description: 'IoT device management and secure connectivity service',
    policies: 4,
    lastActivity: '10 minutes ago',
  },
];



const getProductIcon = (name: string) => {
  switch (name.toLowerCase()) {
    case 'lender dashboard':
    case 'platform dashboard':
      return <DashboardIcon />;
    case 'lisa':
      return <SecurityIcon />;
    case 'sentinel':
      return <SecurityIcon />;
    case 'bankconnect':
      return <BankIcon />;
    case 'deviceconnect':
      return <DeviceIcon />;
    default:
      return <ComputerIcon />;
  }
};

const getTypeColor = (type: string) => {
  switch (type) {
    case 'api':
      return '#0969da';
    case 'web':
      return '#1f883d';
    case 'service':
      return '#8250df';
    default:
      return '#656d76';
  }
};

const ProductsPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredProducts, setFilteredProducts] = useState(mockProducts);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    const filtered = mockProducts.filter(product =>
      product.name.toLowerCase().includes(query.toLowerCase()) ||
      product.type.toLowerCase().includes(query.toLowerCase()) ||
      product.description.toLowerCase().includes(query.toLowerCase())
    );
    setFilteredProducts(filtered);
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header Section - GitHub Style */}
      <Box sx={{ mb: 4 }}>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
          <Box>
            <Typography 
              variant="h4" 
              component="h1" 
              sx={{ 
                fontWeight: 600, 
                fontSize: '2rem',
                color: '#24292f',
                mb: 1
              }}
            >
              Products
            </Typography>
            <Typography 
              variant="body1" 
              sx={{ 
                color: '#656d76',
                fontSize: '1rem'
              }}
            >
              Manage and monitor your financial technology products and services
            </Typography>
          </Box>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            sx={{
              backgroundColor: '#1f883d',
              '&:hover': { backgroundColor: '#1a7f37' },
              borderRadius: '6px',
              textTransform: 'none',
              fontWeight: 500,
              px: 3,
              py: 1
            }}
          >
            New product
          </Button>
        </Box>

        {/* Search and Stats Bar */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <TextField
            placeholder="Find a product..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            size="small"
            sx={{
              width: 320,
              '& .MuiOutlinedInput-root': {
                borderRadius: '6px',
                backgroundColor: '#f6f8fa',
                '&:hover': { backgroundColor: '#ffffff' },
                '&.Mui-focused': { backgroundColor: '#ffffff' }
              }
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon sx={{ color: '#656d76', fontSize: 20 }} />
                </InputAdornment>
              ),
            }}
          />
          <Typography variant="body2" sx={{ color: '#656d76' }}>
            {filteredProducts.length} {filteredProducts.length === 1 ? 'product' : 'products'}
          </Typography>
        </Box>
      </Box>

      {/* Products Grid - GitHub Repository Style */}
      <Grid container spacing={2}>
        {filteredProducts.map((product) => (
          <Grid item xs={12} key={product.id}>
            <Card 
              sx={{ 
                border: '1px solid #d1d9e0',
                borderRadius: '6px',
                boxShadow: 'none',
                '&:hover': {
                  borderColor: '#0969da',
                  boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                },
                transition: 'all 0.2s ease'
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                  <Box display="flex" alignItems="flex-start" gap={2} flex={1}>
                    {/* Product Icon */}
                    <Avatar
                      sx={{
                        width: 40,
                        height: 40,
                        backgroundColor: getTypeColor(product.type),
                        color: 'white'
                      }}
                    >
                      {getProductIcon(product.name)}
                    </Avatar>

                    {/* Product Info */}
                    <Box flex={1}>
                      <Box display="flex" alignItems="center" gap={1} mb={1}>
                        <Typography
                          variant="h6"
                          component="h3"
                          sx={{
                            fontWeight: 600,
                            fontSize: '1.125rem',
                            color: '#0969da',
                            textDecoration: 'none',
                            '&:hover': { textDecoration: 'underline' },
                            cursor: 'pointer'
                          }}
                        >
                          {product.name}
                        </Typography>
                        <Chip
                          label={product.type.toUpperCase()}
                          size="small"
                          sx={{
                            backgroundColor: getTypeColor(product.type),
                            color: 'white',
                            fontWeight: 500,
                            fontSize: '0.75rem',
                            height: 20
                          }}
                        />
                        <Box display="flex" alignItems="center" gap={0.5}>
                          <CircleIcon 
                            sx={{ 
                              fontSize: 8, 
                              color: product.status === 'active' ? '#1f883d' : 
                                     product.status === 'maintenance' ? '#fb8500' : '#da3633'
                            }} 
                          />
                          <Typography
                            variant="caption"
                            sx={{
                              color: product.status === 'active' ? '#1f883d' : 
                                     product.status === 'maintenance' ? '#fb8500' : '#da3633',
                              fontWeight: 500,
                              textTransform: 'capitalize'
                            }}
                          >
                            {product.status}
                          </Typography>
                        </Box>
                      </Box>

                      <Typography
                        variant="body2"
                        sx={{ color: '#656d76', mb: 2, lineHeight: 1.5 }}
                      >
                        {product.description}
                      </Typography>

                      <Stack direction="row" spacing={3} alignItems="center">
                        <Typography variant="body2" sx={{ color: '#656d76', fontSize: '0.875rem' }}>
                          <strong>{product.policies}</strong> policies
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#656d76', fontSize: '0.875rem' }}>
                          Last activity: {product.lastActivity}
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#656d76', fontSize: '0.875rem' }}>
                          Created: {new Date(product.createdAt).toLocaleDateString()}
                        </Typography>
                      </Stack>
                    </Box>
                  </Box>

                  {/* Actions */}
                  <Stack direction="row" spacing={1}>
                    <Tooltip title="View details">
                      <IconButton 
                        size="small" 
                        sx={{ 
                          color: '#656d76',
                          '&:hover': { color: '#0969da', backgroundColor: '#f6f8fa' }
                        }}
                      >
                        <ViewIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Edit product">
                      <IconButton 
                        size="small"
                        sx={{ 
                          color: '#656d76',
                          '&:hover': { color: '#0969da', backgroundColor: '#f6f8fa' }
                        }}
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete product">
                      <IconButton 
                        size="small"
                        sx={{ 
                          color: '#656d76',
                          '&:hover': { color: '#da3633', backgroundColor: '#ffebe9' }
                        }}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Stack>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Empty State */}
      {filteredProducts.length === 0 && (
        <Box 
          display="flex" 
          flexDirection="column" 
          alignItems="center" 
          justifyContent="center"
          sx={{ py: 8 }}
        >
          <ComputerIcon sx={{ fontSize: 64, color: '#656d76', mb: 2 }} />
          <Typography variant="h6" sx={{ color: '#24292f', mb: 1 }}>
            No products found
          </Typography>
          <Typography variant="body2" sx={{ color: '#656d76', mb: 3 }}>
            {searchQuery ? 'Try adjusting your search terms.' : 'Get started by adding your first product.'}
          </Typography>
          {!searchQuery && (
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              sx={{
                backgroundColor: '#1f883d',
                '&:hover': { backgroundColor: '#1a7f37' },
                borderRadius: '6px',
                textTransform: 'none',
                fontWeight: 500
              }}
            >
              Add your first product
            </Button>
          )}
        </Box>
      )}
    </Box>
  );
};

export default ProductsPage;
