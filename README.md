# OPA Dashboard - Styra DAS-like Frontend

A professional, clean, and highly functional web-based frontend application built with React.js that provides a user experience similar to Styra DAS for managing OPA-based backend services.

## 🚀 Features

### Core Functionality
- **Dashboard Overview**: High-level metrics and system health monitoring
- **Systems Management**: CRUD operations for connected systems and applications
- **Policy Management**: Rich Rego policy editor with validation and testing
- **Decision Log**: Comprehensive audit trail with advanced filtering
- **Data Management**: Upload and manage data bundles for policies
- **Settings**: Application configuration and user management

### Technical Highlights
- **Modern React**: Built with React 19 and TypeScript for type safety
- **Material-UI**: Professional enterprise-grade UI components
- **React Query**: Efficient server state management and caching
- **Monaco Editor**: Rich code editing experience for Rego policies
- **Responsive Design**: Desktop-first with tablet compatibility
- **Industry Standards**: Clean architecture following React best practices

## 🛠️ Technology Stack

- **Frontend Framework**: React 19 with TypeScript
- **Build Tool**: Vite 7 for fast development and building
- **UI Library**: Material-UI (MUI) for consistent design
- **State Management**: TanStack Query (React Query) for server state
- **Routing**: React Router DOM
- **HTTP Client**: Axios with interceptors and retry logic
- **Form Management**: React Hook Form with Zod validation
- **Code Editor**: Monaco Editor for policy editing
- **Charts**: Recharts for data visualization
- **Styling**: Emotion (CSS-in-JS) with Material-UI theming

## 📁 Project Structure

```
src/
├── assets/                  # Images, fonts, icons
├── components/              # Reusable UI components
├── constants/               # Global constants and configurations
├── hooks/                   # Custom React hooks
├── layouts/                 # Page layouts
├── lib/                     # Utility functions and API client
├── pages/                   # Top-level page components
├── services/                # API service modules
├── store/                   # Global state management
├── styles/                  # Global styles and theming
├── types/                   # TypeScript type definitions
├── App.tsx                  # Main application component
└── main.tsx                 # Application entry point
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd frontend-red-gate
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   ```
   Edit `.env` file to configure your API endpoints:
   ```env
   VITE_API_BASE_URL=http://localhost:8080
   VITE_APP_NAME=OPA Dashboard
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```
   The application will be available at `http://localhost:5173`

### Development Commands

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Run linting
npm run lint
```

## 🏗️ Architecture Overview

### Component Architecture
- **Atomic Design**: Components organized from atoms to organisms
- **Feature-based Structure**: Related components grouped by feature
- **Separation of Concerns**: Clear separation between UI, logic, and data

### State Management
- **Server State**: React Query for API data fetching and caching
- **Local State**: React hooks (useState, useReducer) for component state
- **Global UI State**: React Context for theme, sidebar state, etc.

### API Integration
- **Centralized Client**: Axios instance with interceptors
- **Service Layer**: Dedicated API service modules
- **Error Handling**: Consistent error handling and user feedback
- **Retry Logic**: Automatic retry for failed requests

## 🎨 Design System

### Theme Configuration
- **Material-UI Theme**: Customized for professional appearance
- **Color Palette**: Consistent color scheme across the application
- **Typography**: Clean, readable font hierarchy
- **Spacing**: Consistent spacing using Material-UI spacing system

### Component Guidelines
- **Consistent Styling**: All components follow the design system
- **Responsive Design**: Mobile-first approach with desktop optimization
- **Accessibility**: WCAG compliant components
- **Loading States**: Consistent loading indicators and skeletons

## 🔧 Configuration

### Environment Variables
```env
# API Configuration
VITE_API_BASE_URL=http://localhost:8080

# Application Configuration
VITE_APP_NAME=OPA Dashboard
VITE_APP_VERSION=1.0.0

# Development Configuration
VITE_DEV_MODE=true
VITE_ENABLE_MOCK_DATA=true
```

### API Endpoints
The application expects the following API endpoints to be available:

- `GET /api/dashboard/summary` - Dashboard metrics
- `GET /api/systems` - List systems
- `POST /api/systems` - Create system
- `GET /api/policies` - List policies
- `POST /api/policies` - Create policy
- `GET /api/decisions` - Decision log
- `GET /api/data` - Data bundles

## 🧪 Testing

### Running Tests
```bash
# Run unit tests
npm run test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

### Testing Strategy
- **Unit Tests**: Component and utility function testing
- **Integration Tests**: API service and hook testing
- **E2E Tests**: Critical user journey testing

## 📦 Building for Production

### Build Process
```bash
# Create production build
npm run build

# Preview production build locally
npm run preview
```

### Deployment
The build process creates optimized static files in the `dist/` directory that can be deployed to any static hosting service:

- **Vercel**: `vercel --prod`
- **Netlify**: Deploy `dist/` folder
- **AWS S3**: Upload `dist/` contents
- **Docker**: Use provided Dockerfile

## 🔒 Security Considerations

- **Authentication**: JWT token-based authentication
- **Authorization**: Role-based access control
- **API Security**: HTTPS enforcement and CORS configuration
- **Input Validation**: Client-side and server-side validation
- **XSS Protection**: Sanitized user inputs and CSP headers

## 🚀 Performance Optimizations

- **Code Splitting**: Lazy loading of route components
- **Bundle Optimization**: Tree shaking and minification
- **Caching**: React Query caching and HTTP caching
- **Image Optimization**: Optimized asset loading
- **Lighthouse Score**: 90+ performance score target

## 🤝 Contributing

### Development Workflow
1. Create feature branch from `main`
2. Follow coding standards and conventions
3. Write tests for new functionality
4. Submit pull request with description

### Code Standards
- **TypeScript**: Strict type checking enabled
- **ESLint**: Enforced code quality rules
- **Prettier**: Consistent code formatting
- **Conventional Commits**: Standardized commit messages

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the API integration guide

## 🗺️ Roadmap

### Upcoming Features
- [ ] Real-time policy testing sandbox
- [ ] Advanced decision log analytics
- [ ] Policy version comparison
- [ ] Bulk policy operations
- [ ] Advanced user management
- [ ] API key management
- [ ] Dark mode support
- [ ] Mobile responsive improvements
