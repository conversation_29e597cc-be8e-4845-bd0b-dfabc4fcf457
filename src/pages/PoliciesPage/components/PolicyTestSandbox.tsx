import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Paper,
  Grid,
  Alert,
  Chip,
} from '@mui/material';
import {
  PlayArrow as TestIcon,
  Clear as ClearIcon,
  ContentCopy as CopyIcon,
} from '@mui/icons-material';
import { Editor } from '@monaco-editor/react';

interface PolicyTestSandboxProps {
  policyContent: string;
  onTest?: (content: string, input: any) => void;
}

interface TestResult {
  result: {
    allow: boolean;
    reason?: string;
    metadata?: Record<string, any>;
  };
  duration: number;
  errors?: string[];
}

const PolicyTestSandbox: React.FC<PolicyTestSandboxProps> = ({
  policyContent,
  onTest,
}) => {
  const [inputJson, setInputJson] = useState(`{
  "user": {
    "type": "admin",
    "id": "user123"
  },
  "transaction": {
    "amount": 1000
  }
}`);
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleTest = async () => {
    if (!policyContent.trim()) {
      setError('Policy content is required');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      // Parse input JSON
      const parsedInput = JSON.parse(inputJson);
      
      // For demo purposes, simulate a test result
      // In real implementation, this would call the backend API
      const mockResult: TestResult = {
        result: {
          allow: Math.random() > 0.5,
          reason: 'Policy evaluation completed',
          metadata: {
            evaluatedRules: ['user_type_allowed', 'output'],
            executionTime: Math.floor(Math.random() * 50) + 5,
          },
        },
        duration: Math.floor(Math.random() * 50) + 5,
      };

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setTestResult(mockResult);
      
      if (onTest) {
        onTest(policyContent, parsedInput);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to test policy');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearInput = () => {
    setInputJson('{}');
  };

  const handleClearResult = () => {
    setTestResult(null);
    setError(null);
  };

  const handleCopyResult = () => {
    if (testResult) {
      navigator.clipboard.writeText(JSON.stringify(testResult, null, 2));
    }
  };

  const loadSampleInput = (sample: string) => {
    const samples = {
      basic: `{
  "user": {
    "type": "admin",
    "id": "user123"
  },
  "transaction": {
    "amount": 1000
  }
}`,
      complex: `{
  "user": {
    "type": "user",
    "id": "user456",
    "roles": ["viewer", "editor"]
  },
  "resource": {
    "type": "document",
    "id": "doc789",
    "owner": "user123"
  },
  "action": "read"
}`,
      taskType: `{
  "user": {
    "type": "admin",
    "id": "user123"
  },
  "task": {
    "type": "mfl-lap-sanction",
    "status": "APPROVED",
    "createdBy": "user123"
  },
  "action": "delete"
}`,
    };
    setInputJson(samples[sample as keyof typeof samples] || samples.basic);
  };

  return (
    <Box sx={{ p: 3, height: '500px', display: 'flex', flexDirection: 'column' }}>
      <Typography variant="h6" gutterBottom>
        Policy Test Sandbox
      </Typography>
      <Typography variant="body2" color="textSecondary" paragraph>
        Test your policy with different input scenarios to verify its behavior.
      </Typography>

      {/* Sample Input Buttons */}
      <Box display="flex" gap={1} mb={2}>
        <Button size="small" onClick={() => loadSampleInput('basic')}>
          Basic Sample
        </Button>
        <Button size="small" onClick={() => loadSampleInput('complex')}>
          Complex Sample
        </Button>
        <Button size="small" onClick={() => loadSampleInput('taskType')}>
          Task Type Sample
        </Button>
      </Box>

      <Grid container spacing={2} sx={{ flexGrow: 1 }}>
        {/* Input Panel */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="subtitle1" fontWeight="bold">
                  Input JSON
                </Typography>
                <Button
                  size="small"
                  startIcon={<ClearIcon />}
                  onClick={handleClearInput}
                >
                  Clear
                </Button>
              </Box>
            </Box>
            <Box sx={{ flexGrow: 1 }}>
              <Editor
                height="100%"
                defaultLanguage="json"
                value={inputJson}
                onChange={(value) => setInputJson(value || '{}')}
                options={{
                  minimap: { enabled: false },
                  lineNumbers: 'on',
                  wordWrap: 'on',
                  automaticLayout: true,
                  fontSize: 12,
                  tabSize: 2,
                }}
                theme="vs-light"
              />
            </Box>
          </Paper>
        </Grid>

        {/* Output Panel */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="subtitle1" fontWeight="bold">
                  Test Result
                </Typography>
                <Box display="flex" gap={1}>
                  {testResult && (
                    <Button
                      size="small"
                      startIcon={<CopyIcon />}
                      onClick={handleCopyResult}
                    >
                      Copy
                    </Button>
                  )}
                  <Button
                    size="small"
                    startIcon={<ClearIcon />}
                    onClick={handleClearResult}
                  >
                    Clear
                  </Button>
                </Box>
              </Box>
            </Box>
            <Box sx={{ flexGrow: 1, p: 2, overflow: 'auto' }}>
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}
              
              {testResult && (
                <Box>
                  <Box display="flex" alignItems="center" gap={1} mb={2}>
                    <Chip
                      label={testResult.result.allow ? 'ALLOW' : 'DENY'}
                      color={testResult.result.allow ? 'success' : 'error'}
                    />
                    <Typography variant="body2" color="textSecondary">
                      {testResult.duration}ms
                    </Typography>
                  </Box>
                  
                  {testResult.result.reason && (
                    <Box mb={2}>
                      <Typography variant="subtitle2" gutterBottom>
                        Reason:
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        {testResult.result.reason}
                      </Typography>
                    </Box>
                  )}
                  
                  {testResult.result.metadata && (
                    <Box mb={2}>
                      <Typography variant="subtitle2" gutterBottom>
                        Metadata:
                      </Typography>
                      <Box
                        component="pre"
                        sx={{
                          fontSize: '0.75rem',
                          backgroundColor: 'grey.100',
                          p: 1,
                          borderRadius: 1,
                          overflow: 'auto',
                        }}
                      >
                        {JSON.stringify(testResult.result.metadata, null, 2)}
                      </Box>
                    </Box>
                  )}
                  
                  {testResult.errors && testResult.errors.length > 0 && (
                    <Box>
                      <Typography variant="subtitle2" gutterBottom color="error">
                        Errors:
                      </Typography>
                      {testResult.errors.map((error, index) => (
                        <Alert key={index} severity="error" sx={{ mb: 1 }}>
                          {error}
                        </Alert>
                      ))}
                    </Box>
                  )}
                </Box>
              )}
              
              {!testResult && !error && (
                <Typography variant="body2" color="textSecondary" textAlign="center">
                  Click "Test Policy" to see the evaluation result
                </Typography>
              )}
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Test Button */}
      <Box sx={{ mt: 2, textAlign: 'center' }}>
        <Button
          variant="contained"
          size="large"
          startIcon={<TestIcon />}
          onClick={handleTest}
          disabled={isLoading || !policyContent.trim()}
        >
          {isLoading ? 'Testing...' : 'Test Policy'}
        </Button>
      </Box>
    </Box>
  );
};

export default PolicyTestSandbox;
