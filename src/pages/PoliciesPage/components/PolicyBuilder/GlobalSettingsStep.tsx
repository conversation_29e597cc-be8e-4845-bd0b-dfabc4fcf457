import React from 'react';
import {
  <PERSON>,
  Typography,
  FormControlLabel,
  Switch,
  Chip,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  OutlinedInput,
  SelectChangeEvent,
} from '@mui/material';
import { OSO_COLORS } from '@/styles/osoTheme';

interface GlobalSettingsStepProps {
  data: {
    globalSettings: {
      addConditionButton: boolean;
      allowUserTypeCheck: boolean;
      allowedUserTypes: string[];
    };
  };
  onChange: (updates: any) => void;
}

const predefinedUserTypes = [
  'admin',
  'user',
  'manager',
  'editor',
  'viewer',
  'moderator',
  'analyst',
  'guest',
];

const GlobalSettingsStep: React.FC<GlobalSettingsStepProps> = ({ data, onChange }) => {
  const handleGlobalSettingChange = (setting: string, value: any) => {
    onChange({
      globalSettings: {
        ...data.globalSettings,
        [setting]: value,
      },
    });
  };

  const handleUserTypesChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    handleGlobalSettingChange('allowedUserTypes', typeof value === 'string' ? value.split(',') : value);
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ mb: 2 }}>
        <Typography
          variant="subtitle1"
          sx={{
            fontWeight: 600,
            color: OSO_COLORS.textPrimary,
            mb: 0.5,
            fontSize: '1rem',
          }}
        >
          Global Policy Settings
        </Typography>
        <Typography
          variant="caption"
          sx={{
            color: OSO_COLORS.textSecondary,
            fontSize: '0.75rem',
          }}
        >
          Configure global settings that apply to your entire policy.
        </Typography>
      </Box>

      {/* GitHub-style Settings Layout */}
      <Box sx={{ flexGrow: 1, display: 'flex', gap: 3 }}>
        {/* Left Column - Settings */}
        <Box sx={{ flex: 2 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {/* Button Configuration */}
            <Box
              sx={{
                p: 2,
                border: `1px solid ${OSO_COLORS.border}`,
                borderRadius: 1,
                bgcolor: OSO_COLORS.background,
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, fontSize: '0.875rem' }}>
                  Button Configuration
                </Typography>
              </Box>
              <Typography variant="caption" sx={{ color: OSO_COLORS.textSecondary, fontSize: '0.75rem', mb: 2, display: 'block' }}>
                Control how action buttons behave in your application.
              </Typography>

              <FormControlLabel
                control={
                  <Switch
                    checked={data.globalSettings.addConditionButton}
                    onChange={(e) => handleGlobalSettingChange('addConditionButton', e.target.checked)}
                    size="small"
                  />
                }
                label={
                  <Box>
                    <Typography variant="body2" sx={{ fontSize: '0.875rem', fontWeight: 500 }}>
                      Enable Add Condition Button
                    </Typography>
                    <Typography variant="caption" sx={{ color: OSO_COLORS.textSecondary, fontSize: '0.75rem' }}>
                      When enabled, users will see an "Add Condition" button in the interface
                    </Typography>
                  </Box>
                }
                sx={{ alignItems: 'flex-start', ml: 0 }}
              />
            </Box>

            {/* User Type Validation */}
            <Box
              sx={{
                p: 2,
                border: `1px solid ${OSO_COLORS.border}`,
                borderRadius: 1,
                bgcolor: OSO_COLORS.background,
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, fontSize: '0.875rem' }}>
                  User Type Validation
                </Typography>
              </Box>
              <Typography variant="caption" sx={{ color: OSO_COLORS.textSecondary, fontSize: '0.75rem', mb: 2, display: 'block' }}>
                Configure which user types are allowed to access resources controlled by this policy.
              </Typography>

              <FormControlLabel
                control={
                  <Switch
                    checked={data.globalSettings.allowUserTypeCheck}
                    onChange={(e) => handleGlobalSettingChange('allowUserTypeCheck', e.target.checked)}
                    size="small"
                  />
                }
                label={
                  <Box>
                    <Typography variant="body2" sx={{ fontSize: '0.875rem', fontWeight: 500 }}>
                      Enable User Type Checking
                    </Typography>
                    <Typography variant="caption" sx={{ color: OSO_COLORS.textSecondary, fontSize: '0.75rem' }}>
                      When enabled, the policy will validate user types before granting access
                    </Typography>
                  </Box>
                }
                sx={{ alignItems: 'flex-start', ml: 0, mb: 2 }}
              />

              {data.globalSettings.allowUserTypeCheck && (
                <Box>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ fontSize: '0.8rem' }}>Allowed User Types</InputLabel>
                    <Select
                      multiple
                      value={data.globalSettings.allowedUserTypes}
                      onChange={handleUserTypesChange}
                      input={<OutlinedInput label="Allowed User Types" />}
                      renderValue={(selected) => (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {selected.map((value) => (
                            <Chip key={value} label={value} size="small" sx={{ fontSize: '0.7rem', height: 20 }} />
                          ))}
                        </Box>
                      )}
                      sx={{ fontSize: '0.875rem' }}
                    >
                      {predefinedUserTypes.map((type) => (
                        <MenuItem key={type} value={type}>
                          <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                            {type}
                          </Typography>
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  <Typography variant="caption" sx={{ color: OSO_COLORS.textSecondary, fontSize: '0.75rem', mt: 1, display: 'block' }}>
                    Select the user types that should be allowed access.
                  </Typography>
                </Box>
              )}
            </Box>
          </Box>
        </Box>

        {/* Right Column - Preview */}
        <Box sx={{ flex: 1 }}>
          <Box
            sx={{
              p: 2,
              bgcolor: OSO_COLORS.surface,
              border: `1px solid ${OSO_COLORS.border}`,
              borderRadius: 1,
              height: 'fit-content',
            }}
          >
            <Typography variant="subtitle2" sx={{ fontWeight: 600, fontSize: '0.875rem', mb: 1 }}>
              Policy Behavior Summary
            </Typography>

            <Box sx={{ mb: 2 }}>
              <Typography variant="caption" sx={{ fontSize: '0.75rem', mb: 1, display: 'block', fontWeight: 600 }}>
                Generated Policy Features:
              </Typography>
              <Box display="flex" flexDirection="column" gap={0.5}>
                <Chip
                  label="Task-based Authorization"
                  sx={{
                    bgcolor: `${OSO_COLORS.primary}15`,
                    color: OSO_COLORS.primary,
                    fontSize: '0.65rem',
                    height: 20,
                    justifyContent: 'flex-start',
                  }}
                  size="small"
                />
                <Chip
                  label="Status-based Permissions"
                  sx={{
                    bgcolor: `${OSO_COLORS.accent}15`,
                    color: OSO_COLORS.accent,
                    fontSize: '0.65rem',
                    height: 20,
                    justifyContent: 'flex-start',
                  }}
                  size="small"
                />
                {data.globalSettings.allowUserTypeCheck && (
                  <Chip
                    label="User Type Validation"
                    sx={{
                      bgcolor: `${OSO_COLORS.success}15`,
                      color: OSO_COLORS.success,
                      fontSize: '0.65rem',
                      height: 20,
                      justifyContent: 'flex-start',
                    }}
                    size="small"
                  />
                )}
                {data.globalSettings.addConditionButton && (
                  <Chip
                    label="Conditional Buttons"
                    sx={{
                      bgcolor: `${OSO_COLORS.warning}15`,
                      color: OSO_COLORS.warning,
                      fontSize: '0.65rem',
                      height: 20,
                      justifyContent: 'flex-start',
                    }}
                    size="small"
                  />
                )}
              </Box>
            </Box>

            <Divider sx={{ my: 1.5 }} />

            <Typography variant="caption" sx={{ fontSize: '0.75rem', mb: 1, display: 'block', fontWeight: 600 }}>
              Policy Validation Rules:
            </Typography>
            <Box component="ul" sx={{ pl: 2, m: 0, fontSize: '0.75rem' }}>
              {data.globalSettings.allowUserTypeCheck ? (
                <li>
                  <Typography variant="caption" sx={{ fontSize: '0.75rem' }}>
                    Users must have one of these types: {data.globalSettings.allowedUserTypes.join(', ')}
                  </Typography>
                </li>
              ) : (
                <li>
                  <Typography variant="caption" sx={{ fontSize: '0.75rem', color: OSO_COLORS.textSecondary }}>
                    No user type validation (all user types allowed)
                  </Typography>
                </li>
              )}
              <li>
                <Typography variant="caption" sx={{ fontSize: '0.75rem' }}>
                  Task permissions are based on status and button configurations
                </Typography>
              </li>
              <li>
                <Typography variant="caption" sx={{ fontSize: '0.75rem' }}>
                  Button visibility is controlled by condition rules
                </Typography>
              </li>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default GlobalSettingsStep;
