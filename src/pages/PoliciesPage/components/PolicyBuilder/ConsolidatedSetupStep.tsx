import React from 'react';
import {
  Box,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Divider,
  Button,
  Tooltip,
} from '@mui/material';
import {
  Security as SecurityIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
} from '@mui/icons-material';
import { OSO_COLORS } from '@/styles/osoTheme';
import { TaskTypesContent } from './TaskTypesStep';
import ProductSelector from '../ProductSelector';

interface ConsolidatedSetupStepProps {
  data: any;
  onChange: (updates: any) => void;
  onBasicInfoChange: (field: string, value: string) => void;
  sidebarVisible?: boolean;
  onToggleSidebar?: () => void;
}



const ConsolidatedSetupStep: React.FC<ConsolidatedSetupStepProps> = ({
  data,
  onChange,
  onBasicInfoChange,
  sidebarVisible = false,
  onToggleSidebar
}) => {
  const handleBasicInfoChange = (field: string, value: string) => {
    onBasicInfoChange(field, value);
  };

  return (
    <Box sx={{
      height: 'fit-content',
      minHeight: '100%',
      display: 'flex',
      flexDirection: 'column',
      gap: 2,
      position: 'relative',
    }}>
      {/* Single Unified Card */}
      <Box
        sx={{
          border: `1px solid ${OSO_COLORS.border}`,
          borderRadius: 1,
          bgcolor: '#ffffff',
          overflow: 'visible',
          display: 'flex',
          flexDirection: 'column',
          minHeight: 'fit-content',
        }}
      >
        {/* Compact Header */}
        <Box
          sx={{
            p: 2,
            bgcolor: OSO_COLORS.surface,
            borderBottom: `1px solid ${OSO_COLORS.border}`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
            <SecurityIcon sx={{ color: OSO_COLORS.primary, fontSize: 20 }} />
            <Box>
              <Typography variant="h6" fontWeight={600} sx={{ fontSize: '1rem' }}>
                Policy Configuration
              </Typography>
              <Typography variant="body2" color="textSecondary" sx={{ fontSize: '0.8rem' }}>
                Set up basic information and configure task types with permissions
              </Typography>
            </Box>
          </Box>

          {/* Code Preview Toggle */}
          {onToggleSidebar && (
            <Tooltip title="Toggle OPA Code Preview">
              <Button
                variant="outlined"
                size="small"
                startIcon={sidebarVisible ? <VisibilityOffIcon /> : <VisibilityIcon />}
                onClick={onToggleSidebar}
                sx={{
                  textTransform: 'none',
                  fontSize: '0.75rem',
                  px: 1.5,
                  py: 0.5,
                  minWidth: 'auto',
                }}
              >
                {sidebarVisible ? 'Hide' : 'Show'} Code
              </Button>
            </Tooltip>
          )}
        </Box>

        {/* Content */}
        <Box sx={{
          p: 3,
          overflow: 'visible',
          minHeight: 'fit-content',
        }}>
          {/* Basic Information Section */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="subtitle1" fontWeight={600} sx={{ fontSize: '0.95rem', mb: 2 }}>
              Basic Information
            </Typography>

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {/* Policy Name */}
              <TextField
                fullWidth
                label="Policy Name"
                value={data.name}
                onChange={(e) => handleBasicInfoChange('name', e.target.value)}
                placeholder="e.g., User Access Control"
                required
                variant="outlined"
                size="small"
                sx={{
                  '& .MuiInputLabel-root': { fontSize: '0.875rem' },
                  '& .MuiOutlinedInput-root': { fontSize: '0.875rem', borderRadius: 1 },
                }}
              />

              {/* Product Selection */}
              <ProductSelector
                value={data.productId || ''}
                onChange={(productId) => handleBasicInfoChange('productId', productId)}
                error={!data.productId ? 'Please select a product' : ''}
                helperText="Choose the product this policy will govern"
              />

              {/* Description */}
              <Box>
                <TextField
                  fullWidth
                  label="Description (Optional)"
                  value={data.description}
                  onChange={(e) => handleBasicInfoChange('description', e.target.value)}
                  placeholder="Brief description of what this policy controls..."
                  multiline
                  rows={2}
                  variant="outlined"
                  size="small"
                  sx={{
                    '& .MuiInputLabel-root': { fontSize: '0.875rem' },
                    '& .MuiOutlinedInput-root': { fontSize: '0.875rem', borderRadius: 1 },
                  }}
                />
              </Box>
            </Box>
          </Box>

          {/* Divider */}
          <Divider sx={{ my: 3 }} />

          {/* Task Types & Permissions Section */}
          <Box>
            <Box display="flex" alignItems="center" gap={1} mb={2}>
              <Typography variant="subtitle1" fontWeight={600} sx={{ fontSize: '0.95rem' }}>
                Task Types & Permissions
              </Typography>
              <Chip
                label={`${Object.keys(data.taskTypes || {}).length} types`}
                size="small"
                sx={{ height: 20, fontSize: '0.7rem' }}
              />
            </Box>

            {/* Task Types Content - Without Sidebar */}
            <TaskTypesContent
              data={data}
              onChange={onChange}
            />
          </Box>


        </Box>
      </Box>
    </Box>
  );
};

export default ConsolidatedSetupStep;
