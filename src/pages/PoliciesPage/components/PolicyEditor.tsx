import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Tabs,
  Tab,
  Typography,
  Button,
  Alert,
} from '@mui/material';
import {
  Save as SaveIcon,
  PlayArrow as TestIcon,
  Code as CodeIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { Editor } from '@monaco-editor/react';
import { PolicyParser } from '@/lib/policyParser';
import { ParsedPolicyStructure, PolicyOutput } from '@/types';
import StructuredOutputEditor from './StructuredOutputEditor';
import PolicyTestSandbox from './PolicyTestSandbox';

interface PolicyEditorProps {
  initialContent?: string;
  onSave?: (content: string) => void;
  onTest?: (content: string, input: any) => void;
  readOnly?: boolean;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`policy-editor-tabpanel-${index}`}
      aria-labelledby={`policy-editor-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 0 }}>{children}</Box>}
    </div>
  );
};

const PolicyEditor: React.FC<PolicyEditorProps> = ({
  initialContent = '',
  onSave,
  onTest,
  readOnly = false,
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [policyContent, setPolicyContent] = useState(initialContent);
  const [parsedStructure, setParsedStructure] = useState<ParsedPolicyStructure | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);

  // Parse policy content when it changes
  useEffect(() => {
    try {
      const parsed = PolicyParser.parsePolicyContent(policyContent);
      setParsedStructure(parsed);
      setValidationError(null);
    } catch (error) {
      setValidationError(error instanceof Error ? error.message : 'Failed to parse policy');
    }
  }, [policyContent]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleCodeChange = (value: string | undefined) => {
    if (value !== undefined) {
      setPolicyContent(value);
      setHasUnsavedChanges(true);
    }
  };

  const handleStructuredOutputChange = (output: PolicyOutput) => {
    try {
      const baseContent = policyContent.replace(/output\s*:=\s*\{[\s\S]*?\n\}/, '').trim();
      const newContent = PolicyParser.generatePolicyContent(baseContent, output);
      setPolicyContent(newContent);
      setHasUnsavedChanges(true);
    } catch (error) {
      setValidationError(error instanceof Error ? error.message : 'Failed to update policy');
    }
  };

  const handleSave = () => {
    if (onSave) {
      onSave(policyContent);
      setHasUnsavedChanges(false);
    }
  };

  const handleTest = () => {
    if (onTest) {
      // For now, we'll pass empty input - this will be enhanced with the test sandbox
      onTest(policyContent, {});
    }
  };

  return (
    <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">Policy Editor</Typography>
          <Box display="flex" gap={1}>
            <Button
              variant="outlined"
              startIcon={<TestIcon />}
              onClick={handleTest}
              disabled={!policyContent.trim()}
            >
              Test Policy
            </Button>
            <Button
              variant="contained"
              startIcon={<SaveIcon />}
              onClick={handleSave}
              disabled={!hasUnsavedChanges || readOnly}
            >
              Save
            </Button>
          </Box>
        </Box>
        
        {hasUnsavedChanges && (
          <Alert severity="info" sx={{ mt: 1 }}>
            You have unsaved changes
          </Alert>
        )}
        
        {validationError && (
          <Alert severity="error" sx={{ mt: 1 }}>
            {validationError}
          </Alert>
        )}
      </Box>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab
            icon={<CodeIcon />}
            label="Rego Code"
            id="policy-editor-tab-0"
            aria-controls="policy-editor-tabpanel-0"
          />
          {parsedStructure?.hasStructuredOutput && (
            <Tab
              icon={<SettingsIcon />}
              label="Output Configuration"
              id="policy-editor-tab-1"
              aria-controls="policy-editor-tabpanel-1"
            />
          )}
          <Tab
            icon={<TestIcon />}
            label="Test Sandbox"
            id="policy-editor-tab-2"
            aria-controls="policy-editor-tabpanel-2"
          />
        </Tabs>
      </Box>

      {/* Tab Panels */}
      <Box sx={{ flexGrow: 1, overflow: 'hidden' }}>
        {/* Rego Code Editor */}
        <TabPanel value={activeTab} index={0}>
          <Box sx={{ height: '500px' }}>
            <Editor
              height="100%"
              defaultLanguage="text"
              value={policyContent}
              onChange={handleCodeChange}
              options={{
                readOnly,
                minimap: { enabled: false },
                lineNumbers: 'on',
                wordWrap: 'on',
                automaticLayout: true,
                fontSize: 14,
                tabSize: 2,
                insertSpaces: false,
              }}
              theme="vs-light"
            />
          </Box>
        </TabPanel>

        {/* Structured Output Editor */}
        {parsedStructure?.hasStructuredOutput && (
          <TabPanel value={activeTab} index={1}>
            <StructuredOutputEditor
              output={parsedStructure.output!}
              onChange={handleStructuredOutputChange}
              readOnly={readOnly}
            />
          </TabPanel>
        )}

        {/* Test Sandbox */}
        <TabPanel value={activeTab} index={parsedStructure?.hasStructuredOutput ? 2 : 1}>
          <PolicyTestSandbox
            policyContent={policyContent}
            onTest={onTest}
          />
        </TabPanel>
      </Box>
    </Paper>
  );
};

export default PolicyEditor;
