// API Endpoints
export const API_ENDPOINTS = {
  // Auth
  LOGIN: '/auth/login',
  LOGOUT: '/auth/logout',
  REFRESH: '/auth/refresh',
  
  // Products
  PRODUCTS: '/api/products',
  PRODUCT_BY_ID: (id: string) => `/api/products/${id}`,
  
  // Policies
  POLICIES: '/api/policies',
  POLICY_BY_ID: (id: string) => `/api/policies/${id}`,
  POLICY_VALIDATE: '/api/policies/validate',
  POLICY_TEST: '/api/policies/test',
  POLICY_VERSIONS: (id: string) => `/api/policies/${id}/versions`,
  
  // Decisions
  DECISIONS: '/api/decisions',
  DECISIONS_TRENDS: '/api/decisions/trends',

  // Dashboard
  DASHBOARD_SUMMARY: '/api/dashboard/summary',
  DASHBOARD_METRICS: '/api/dashboard/metrics',
  
  // Users
  USERS: '/api/users',
  USER_BY_ID: (id: string) => `/api/users/${id}`,
  
  // Configuration
  CONFIG: '/api/config',
  API_KEYS: '/api/api-keys',
} as const;

// Application Routes
export const ROUTES = {
  HOME: '/',
  DASHBOARD: '/dashboard',
  PRODUCTS: '/products',
  PRODUCT_DETAIL: '/products/:id',
  POLICIES: '/policies',
  POLICY_DETAIL: '/policies/:id',
  POLICY_EDITOR: '/policies/:id/edit',
  DECISIONS: '/decisions',
  SETTINGS: '/settings',
  USERS: '/settings/users',
  API_KEYS: '/settings/api-keys',
  LOGIN: '/login',
} as const;

// Financial Products
export const FINANCIAL_PRODUCTS = [
  {
    id: 'lender-dashboard',
    name: 'Lender Dashboard',
    type: 'web',
    description: 'Comprehensive lending management platform for financial institutions',
    icon: 'dashboard',
    color: '#1f883d',
  },
  {
    id: 'platform-dashboard',
    name: 'Platform Dashboard',
    type: 'web',
    description: 'Central platform for managing all financial services and operations',
    icon: 'dashboard',
    color: '#0969da',
  },
  {
    id: 'lisa',
    name: 'Lisa',
    type: 'api',
    description: 'AI-powered lending intelligence and risk assessment API',
    icon: 'security',
    color: '#8250df',
  },
  {
    id: 'sentinel',
    name: 'Sentinel',
    type: 'service',
    description: 'Advanced fraud detection and security monitoring service',
    icon: 'security',
    color: '#da3633',
  },
  {
    id: 'bankconnect',
    name: 'BankConnect',
    type: 'api',
    description: 'Secure banking integration and account verification API',
    icon: 'bank',
    color: '#fb8500',
  },
  {
    id: 'deviceconnect',
    name: 'DeviceConnect',
    type: 'service',
    description: 'Device registration and authentication service',
    icon: 'device',
    color: '#1f883d',
  },
] as const;

// Financial Products available for Policy Management (limited subset)
export const POLICY_MANAGEMENT_PRODUCTS = [
  {
    id: 'lender-dashboard',
    name: 'Lender Dashboard',
    type: 'web',
    description: 'Comprehensive lending management platform for financial institutions',
    icon: 'dashboard',
    color: '#1f883d',
  },
  {
    id: 'platform-dashboard',
    name: 'Platform Dashboard',
    type: 'web',
    description: 'Central platform for managing all financial services and operations',
    icon: 'dashboard',
    color: '#0969da',
  },
] as const;

// Product Types
export const PRODUCT_TYPES = [
  { value: 'web', label: 'Web Application' },
  { value: 'api', label: 'API Service' },
  { value: 'service', label: 'Microservice' },
  { value: 'platform', label: 'Platform' },
] as const;

// System Status
export const SYSTEM_STATUS = [
  { value: 'active', label: 'Active', color: 'success' },
  { value: 'inactive', label: 'Inactive', color: 'error' },
  { value: 'maintenance', label: 'Maintenance', color: 'warning' },
] as const;

// Policy Status
export const POLICY_STATUS = [
  { value: 'draft', label: 'Draft', color: 'info' },
  { value: 'active', label: 'Active', color: 'success' },
  { value: 'deprecated', label: 'Deprecated', color: 'error' },
] as const;

// User Roles
export const USER_ROLES = [
  { value: 'admin', label: 'Administrator' },
  { value: 'editor', label: 'Editor' },
  { value: 'viewer', label: 'Viewer' },
] as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'Access denied.',
  NOT_FOUND: 'Resource not found.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  SERVER_ERROR: 'Internal server error. Please try again later.',
  UNKNOWN_ERROR: 'An unexpected error occurred.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  PRODUCT_CREATED: 'Product created successfully.',
  PRODUCT_UPDATED: 'Product updated successfully.',
  PRODUCT_DELETED: 'Product deleted successfully.',
  POLICY_CREATED: 'Policy created successfully.',
  POLICY_UPDATED: 'Policy updated successfully.',
  POLICY_DELETED: 'Policy deleted successfully.',
  POLICY_DEPLOYED: 'Policy deployed successfully.',
  SETTINGS_SAVED: 'Settings saved successfully.',
} as const;

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
} as const;

// Theme Colors (Material-UI compatible)
export const THEME_COLORS = {
  primary: '#1976d2',
  secondary: '#dc004e',
  success: '#2e7d32',
  warning: '#ed6c02',
  error: '#d32f2f',
  info: '#0288d1',
} as const;

// Chart Colors
export const CHART_COLORS = [
  '#1976d2',
  '#dc004e',
  '#2e7d32',
  '#ed6c02',
  '#d32f2f',
  '#0288d1',
  '#7b1fa2',
  '#388e3c',
] as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_PREFERENCES: 'user_preferences',
  THEME: 'theme',
  SIDEBAR_STATE: 'sidebar_state',
} as const;

// Date Formats
export const DATE_FORMATS = {
  DISPLAY: 'MMM dd, yyyy HH:mm',
  API: 'yyyy-MM-dd',
  TIMESTAMP: 'yyyy-MM-dd HH:mm:ss',
} as const;

// Validation Rules
export const VALIDATION = {
  MIN_PASSWORD_LENGTH: 8,
  MAX_POLICY_NAME_LENGTH: 100,
  MAX_SYSTEM_NAME_LENGTH: 50,
  MAX_DESCRIPTION_LENGTH: 500,
} as const;
