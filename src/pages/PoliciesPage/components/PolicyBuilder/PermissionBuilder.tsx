import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  <PERSON>alogContent,
  <PERSON>alogActions,
  Button,
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Card,
  CardContent,
  Chip,
  Grid,
  Alert,
  Tooltip,
  IconButton,
  Divider,
  Tab,
  Tabs,

  Paper,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Collapse,
} from '@mui/material';
import {
  Info as InfoIcon,
  Security as SecurityIcon,
  Person as PersonIcon,
  Group as GroupIcon,
  Public as PublicIcon,
  Code as CodeIcon,
  Assignment as TemplateIcon,
  Build as BuildIcon,
  CheckCircle as CheckCircleIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
} from '@mui/icons-material';
import { PolicyParser } from '@/lib/policyParser';
import { ButtonCondition } from '@/types';
import { OSO_COLORS } from '@/styles/osoTheme';

interface PermissionBuilderProps {
  open: boolean;
  onClose: () => void;
  onSave: (buttonName: string, condition: ButtonCondition) => void;
  existingButtons: string[];
  title: string;
  initialButton?: {
    name: string;
    condition: ButtonCondition;
  };
}

// Permission templates for common use cases
const PERMISSION_TEMPLATES = [
  {
    id: 'creator-only',
    name: 'Creator Only',
    description: 'Only the person who created the task can perform this action',
    icon: <PersonIcon />,
    buttonName: 'edit',
    condition: { createdBy: 'currentUser' },
    example: 'Perfect for editing personal tasks or documents',
  },
  {
    id: 'creator-and-managers',
    name: 'Creator & Managers',
    description: 'Creator and users with higher privileges can perform this action',
    icon: <GroupIcon />,
    buttonName: 'approve',
    condition: { createdBy: 'currentUserAndAbove' },
    example: 'Great for approval workflows where managers can override',
  },
  {
    id: 'public-access',
    name: 'Public Access',
    description: 'Any authenticated user can perform this action',
    icon: <PublicIcon />,
    buttonName: 'view',
    condition: { createdBy: 'AnyUser' },
    example: 'Suitable for viewing public content or shared resources',
  },
  {
    id: 'custom-rule',
    name: 'Custom Rule',
    description: 'Define your own custom access control logic',
    icon: <CodeIcon />,
    buttonName: 'custom',
    condition: { custom: 'input.user.department == resource.department' },
    example: 'For complex business rules and department-specific access',
  },
];

const PermissionBuilder: React.FC<PermissionBuilderProps> = ({
  open,
  onClose,
  onSave,
  existingButtons,
  title,
  initialButton,
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [activeStep, setActiveStep] = useState(0);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [buttonName, setButtonName] = useState(initialButton?.name || '');
  const [conditionType, setConditionType] = useState<string>(
    initialButton?.condition.createdBy || 'currentUserAndAbove'
  );
  const [customCondition, setCustomCondition] = useState(
    initialButton?.condition.custom || ''
  );
  const [error, setError] = useState('');
  const [showAdvanced, setShowAdvanced] = useState(false);

  const buttonTypes = PolicyParser.getButtonTypes();
  const conditionOptions = PolicyParser.getConditionOptions();

  const handleTemplateSelect = (templateId: string) => {
    const template = PERMISSION_TEMPLATES.find(t => t.id === templateId);
    if (template) {
      setSelectedTemplate(templateId);
      setButtonName(template.buttonName);
      if (template.condition.createdBy) {
        setConditionType(template.condition.createdBy);
        setCustomCondition('');
      } else if (template.condition.custom) {
        setConditionType('custom');
        setCustomCondition(template.condition.custom);
      }
      setActiveStep(1); // Move to configuration step
    }
  };

  const handleStepNext = () => {
    if (activeStep < 2) {
      setActiveStep(activeStep + 1);
    }
  };

  const handleStepBack = () => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1);
    }
  };

  const getConditionIcon = (type: string) => {
    switch (type) {
      case 'currentUser':
        return <PersonIcon />;
      case 'currentUserAndAbove':
        return <GroupIcon />;
      case 'AnyUser':
        return <PublicIcon />;
      case 'custom':
        return <CodeIcon />;
      default:
        return <SecurityIcon />;
    }
  };

  const getConditionDescription = (type: string) => {
    switch (type) {
      case 'currentUser':
        return 'Only the user who created the task can perform this action';
      case 'currentUserAndAbove':
        return 'The creator and users with higher privileges can perform this action';
      case 'AnyUser':
        return 'Any authenticated user can perform this action';
      case 'custom':
        return 'Define a custom Rego condition for this permission';
      default:
        return '';
    }
  };

  const getConditionExample = (type: string) => {
    switch (type) {
      case 'currentUser':
        return 'input.user.id == resource.createdBy';
      case 'currentUserAndAbove':
        return 'input.user.id == resource.createdBy || input.user.role in ["admin", "manager"]';
      case 'AnyUser':
        return 'input.user.authenticated == true';
      case 'custom':
        return 'input.user.department == resource.department';
      default:
        return '';
    }
  };

  const handleSave = () => {
    if (!buttonName.trim()) {
      setError('Permission name is required');
      return;
    }

    if (existingButtons.includes(buttonName) && buttonName !== initialButton?.name) {
      setError('Permission name already exists');
      return;
    }

    if (conditionType === 'custom' && !customCondition.trim()) {
      setError('Custom condition is required');
      return;
    }

    const condition: ButtonCondition = {};
    
    if (conditionType === 'custom') {
      condition.custom = customCondition;
    } else {
      condition.createdBy = conditionType as any;
    }

    onSave(buttonName, condition);
    handleClose();
  };

  const handleClose = () => {
    setActiveTab(0);
    setActiveStep(0);
    setSelectedTemplate(null);
    setButtonName(initialButton?.name || '');
    setConditionType(initialButton?.condition.createdBy || 'currentUserAndAbove');
    setCustomCondition(initialButton?.condition.custom || '');
    setError('');
    setShowAdvanced(false);
    onClose();
  };

  const renderTemplateSelection = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Choose a Permission Template
      </Typography>
      <Typography variant="body2" color="textSecondary" paragraph>
        Start with a pre-built template that matches your use case, or create a custom permission from scratch.
      </Typography>

      <Grid container spacing={2}>
        {PERMISSION_TEMPLATES.map((template) => (
          <Grid item xs={12} sm={6} key={template.id}>
            <Card
              sx={{
                cursor: 'pointer',
                border: selectedTemplate === template.id ? 2 : 1,
                borderColor: selectedTemplate === template.id ? OSO_COLORS.primary : OSO_COLORS.border,
                '&:hover': {
                  borderColor: OSO_COLORS.primary,
                  boxShadow: 2,
                },
                transition: 'all 0.2s ease-in-out',
              }}
              onClick={() => handleTemplateSelect(template.id)}
            >
              <CardContent>
                <Box display="flex" alignItems="center" gap={1} mb={1}>
                  {template.icon}
                  <Typography variant="subtitle1" fontWeight={600}>
                    {template.name}
                  </Typography>
                  {selectedTemplate === template.id && (
                    <CheckCircleIcon color="primary" fontSize="small" />
                  )}
                </Box>
                <Typography variant="body2" color="textSecondary" paragraph>
                  {template.description}
                </Typography>
                <Typography variant="caption" color="textSecondary" fontStyle="italic">
                  {template.example}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <SecurityIcon color="primary" />
          {title}
        </Box>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        <Tabs
          value={activeTab}
          onChange={(_, newValue) => setActiveTab(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider', px: 3, pt: 2 }}
        >
          <Tab
            icon={<TemplateIcon />}
            label="Quick Setup"
            iconPosition="start"
          />
          <Tab
            icon={<BuildIcon />}
            label="Advanced Builder"
            iconPosition="start"
          />
        </Tabs>

        <Box sx={{ p: 3 }}>
          {activeTab === 0 ? (
            // Quick Setup Tab with Step Wizard
            <Box>
              <Stepper activeStep={activeStep} orientation="vertical">
                <Step>
                  <StepLabel>Select Template</StepLabel>
                  <StepContent>
                    {renderTemplateSelection()}
                    <Box sx={{ mt: 2 }}>
                      <Button
                        variant="contained"
                        onClick={handleStepNext}
                        disabled={!selectedTemplate}
                      >
                        Continue
                      </Button>
                    </Box>
                  </StepContent>
                </Step>

                <Step>
                  <StepLabel>Configure Permission</StepLabel>
                  <StepContent>
                    <Grid container spacing={3}>
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Permission Name"
                          value={buttonName}
                          onChange={(e) => setButtonName(e.target.value)}
                          placeholder="e.g., edit, approve, delete"
                          helperText="Choose a descriptive name for this permission"
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <FormControl fullWidth>
                          <InputLabel>Access Control</InputLabel>
                          <Select
                            value={conditionType}
                            onChange={(e) => setConditionType(e.target.value)}
                            label="Access Control"
                          >
                            {conditionOptions.map((option) => (
                              <MenuItem key={option.value} value={option.value}>
                                <Box display="flex" alignItems="center" gap={1}>
                                  {getConditionIcon(option.value)}
                                  <Typography>{option.label}</Typography>
                                </Box>
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12}>
                        <Card variant="outlined" sx={{ bgcolor: OSO_COLORS.surface }}>
                          <CardContent>
                            <Typography variant="subtitle2" gutterBottom>
                              {getConditionDescription(conditionType)}
                            </Typography>

                            {conditionType === 'custom' ? (
                              <TextField
                                fullWidth
                                multiline
                                rows={3}
                                label="Custom Rego Condition"
                                value={customCondition}
                                onChange={(e) => setCustomCondition(e.target.value)}
                                placeholder="Enter your custom Rego condition..."
                                sx={{ mt: 1 }}
                              />
                            ) : (
                              <Paper variant="outlined" sx={{ p: 2, mt: 1, bgcolor: 'grey.50' }}>
                                <Typography variant="caption" color="textSecondary" display="block" mb={1}>
                                  Generated condition:
                                </Typography>
                                <Typography
                                  variant="body2"
                                  fontFamily="monospace"
                                  sx={{ fontSize: '0.8rem' }}
                                >
                                  {getConditionExample(conditionType)}
                                </Typography>
                              </Paper>
                            )}
                          </CardContent>
                        </Card>
                      </Grid>
                    </Grid>

                    <Box sx={{ mt: 2 }}>
                      <Button onClick={handleStepBack} sx={{ mr: 1 }}>
                        Back
                      </Button>
                      <Button
                        variant="contained"
                        onClick={handleStepNext}
                        disabled={!buttonName.trim() || (conditionType === 'custom' && !customCondition.trim())}
                      >
                        Preview
                      </Button>
                    </Box>
                  </StepContent>
                </Step>

                <Step>
                  <StepLabel>Preview & Save</StepLabel>
                  <StepContent>
                    <Alert severity="success" sx={{ mb: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Permission Ready!
                      </Typography>
                      <Typography variant="body2">
                        Your permission has been configured and is ready to save.
                      </Typography>
                    </Alert>

                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          Permission Summary
                        </Typography>
                        <Box display="flex" gap={1} alignItems="center" mb={2}>
                          <Chip label={buttonName} color="primary" />
                          <Typography variant="body2" color="textSecondary">with</Typography>
                          <Chip
                            label={conditionOptions.find(opt => opt.value === conditionType)?.label}
                            color="secondary"
                            variant="outlined"
                          />
                          <Typography variant="body2" color="textSecondary">access</Typography>
                        </Box>

                        <Typography variant="body2" color="textSecondary" paragraph>
                          {getConditionDescription(conditionType)}
                        </Typography>

                        <Divider sx={{ my: 2 }} />

                        <Typography variant="caption" color="textSecondary" display="block" mb={1}>
                          Generated Rego code:
                        </Typography>
                        <Paper variant="outlined" sx={{ p: 2, bgcolor: 'grey.50' }}>
                          <Typography
                            variant="body2"
                            fontFamily="monospace"
                            sx={{ fontSize: '0.8rem' }}
                          >
                            {conditionType === 'custom' ? customCondition : getConditionExample(conditionType)}
                          </Typography>
                        </Paper>
                      </CardContent>
                    </Card>

                    <Box sx={{ mt: 2 }}>
                      <Button onClick={handleStepBack} sx={{ mr: 1 }}>
                        Back
                      </Button>
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={handleSave}
                      >
                        {initialButton ? 'Update Permission' : 'Add Permission'}
                      </Button>
                    </Box>
                  </StepContent>
                </Step>
              </Stepper>
            </Box>
          ) : (
            // Advanced Builder Tab
            <Box>
              <Alert severity="info" sx={{ mb: 3 }}>
                Advanced mode gives you full control over permission configuration with detailed options and custom Rego conditions.
              </Alert>

              <Grid container spacing={3}>
                {/* Permission Name */}
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Permission Type</InputLabel>
                    <Select
                      value={buttonName}
                      onChange={(e) => setButtonName(e.target.value)}
                      label="Permission Type"
                    >
                      {buttonTypes.map((type) => (
                        <MenuItem key={type} value={type}>
                          <Typography>{type}</Typography>
                        </MenuItem>
                      ))}
                      <MenuItem value="custom">Custom Permission</MenuItem>
                    </Select>
                  </FormControl>

                  {buttonName === 'custom' && (
                    <TextField
                      fullWidth
                      label="Custom Permission Name"
                      value={buttonName}
                      onChange={(e) => setButtonName(e.target.value)}
                      sx={{ mt: 2 }}
                      placeholder="e.g., archive, publish, review"
                    />
                  )}
                </Grid>

                {/* Condition Type */}
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Access Control</InputLabel>
                    <Select
                      value={conditionType}
                      onChange={(e) => setConditionType(e.target.value)}
                      label="Access Control"
                    >
                      {conditionOptions.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          <Box display="flex" alignItems="center" gap={1}>
                            {getConditionIcon(option.value)}
                            <Typography>{option.label}</Typography>
                          </Box>
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {/* Advanced Options */}
                <Grid item xs={12}>
                  <Button
                    startIcon={showAdvanced ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    onClick={() => setShowAdvanced(!showAdvanced)}
                    sx={{ mb: 2 }}
                  >
                    Advanced Options
                  </Button>

                  <Collapse in={showAdvanced}>
                    <Card variant="outlined" sx={{ p: 2, mb: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Additional Configuration
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Future enhancements: Time-based restrictions, IP filtering, multi-factor requirements, etc.
                      </Typography>
                    </Card>
                  </Collapse>
                </Grid>

                {/* Condition Details */}
                <Grid item xs={12}>
                  <Card variant="outlined" sx={{ bgcolor: OSO_COLORS.surface }}>
                    <CardContent>
                      <Box display="flex" alignItems="center" gap={1} mb={2}>
                        {getConditionIcon(conditionType)}
                        <Typography variant="h6" fontSize="1rem">
                          {conditionOptions.find(opt => opt.value === conditionType)?.label}
                        </Typography>
                        <Tooltip title="Learn more about this condition type">
                          <IconButton size="small">
                            <InfoIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>

                      <Typography variant="body2" color="textSecondary" paragraph>
                        {getConditionDescription(conditionType)}
                      </Typography>

                      {conditionType === 'custom' ? (
                        <TextField
                          fullWidth
                          multiline
                          rows={4}
                          label="Custom Rego Condition"
                          value={customCondition}
                          onChange={(e) => setCustomCondition(e.target.value)}
                          placeholder="Enter your custom Rego condition..."
                          helperText="Write a Rego expression that evaluates to true when the action should be allowed"
                        />
                      ) : (
                        <Box>
                          <Typography variant="caption" color="textSecondary" display="block" mb={1}>
                            Generated Rego condition:
                          </Typography>
                          <Card variant="outlined" sx={{ bgcolor: 'grey.50' }}>
                            <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                              <Typography
                                variant="body2"
                                fontFamily="monospace"
                                sx={{ fontSize: '0.8rem' }}
                              >
                                {getConditionExample(conditionType)}
                              </Typography>
                            </CardContent>
                          </Card>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                {/* Preview */}
                <Grid item xs={12}>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="subtitle2" gutterBottom>
                    Permission Preview
                  </Typography>
                  <Box display="flex" gap={1} alignItems="center">
                    <Chip
                      label={buttonName || 'Permission Name'}
                      color="primary"
                      variant="outlined"
                    />
                    <Typography variant="body2" color="textSecondary">
                      with
                    </Typography>
                    <Chip
                      label={conditionOptions.find(opt => opt.value === conditionType)?.label}
                      color="secondary"
                      variant="outlined"
                    />
                    <Typography variant="body2" color="textSecondary">
                      access
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          )}

          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 2.5 }}>
        <Button onClick={handleClose} color="inherit">
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          color="primary"
          disabled={!buttonName.trim() || (conditionType === 'custom' && !customCondition.trim())}
        >
          {initialButton ? 'Update Permission' : 'Add Permission'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PermissionBuilder;
