import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>,
  <PERSON>Content,
  Grid,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert,
  Paper,
  Tab,
  Tabs,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Code as CodeIcon,
  Visibility as PreviewIcon,
  CheckCircle as CheckIcon,
} from '@mui/icons-material';
import { OSO_COLORS } from '@/styles/osoTheme';

interface PolicyPreviewStepProps {
  data: {
    name: string;
    description: string;
    packageName: string;
    taskTypes: Record<string, any>;
    globalSettings: {
      addConditionButton: boolean;
      allowUserTypeCheck: boolean;
      allowedUserTypes: string[];
    };
  };
  generatedPolicy: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`preview-tabpanel-${index}`}
      aria-labelledby={`preview-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 0 }}>{children}</Box>}
    </div>
  );
};

const PolicyPreviewStep: React.FC<PolicyPreviewStepProps> = ({ data, generatedPolicy }) => {
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const getConditionLabel = (condition: any) => {
    if (condition.createdBy) {
      switch (condition.createdBy) {
        case 'currentUserAndAbove':
          return 'Current User and Above';
        case 'currentUser':
          return 'Current User Only';
        case 'AnyUser':
          return 'Any User';
        default:
          return condition.createdBy;
      }
    }
    return 'Custom Condition';
  };

  const totalTaskTypes = Object.keys(data.taskTypes).length;
  const totalStatuses = Object.values(data.taskTypes).reduce(
    (acc: number, taskType: any) => acc + Object.keys(taskType.status).length,
    0
  );
  const totalButtons = Object.values(data.taskTypes).reduce(
    (acc: number, taskType: any) =>
      acc +
      Object.values(taskType.status).reduce(
        (statusAcc: number, status: any) => statusAcc + Object.keys(status.buttons).length,
        0
      ),
    0
  );

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ mb: 1.5 }}>
        <Typography
          variant="subtitle1"
          sx={{
            fontWeight: 600,
            color: OSO_COLORS.textPrimary,
            mb: 0.25,
            fontSize: '1rem',
          }}
        >
          Review & Save Policy
        </Typography>
        <Typography
          variant="caption"
          sx={{
            color: OSO_COLORS.textSecondary,
            fontSize: '0.75rem',
          }}
        >
          Review your policy configuration and the generated Rego code before saving.
        </Typography>
      </Box>

      {/* Summary Stats - Horizontal */}
      <Box display="flex" gap={1.5} mb={2}>
        <Card sx={{ textAlign: 'center', bgcolor: `${OSO_COLORS.primary}10`, borderRadius: 1, flex: 1 }}>
          <CardContent sx={{ py: 1, px: 1.5 }}>
            <Typography variant="h6" sx={{ color: OSO_COLORS.primary, fontWeight: 600, fontSize: '1.25rem' }}>
              {totalTaskTypes}
            </Typography>
            <Typography variant="caption" sx={{ color: OSO_COLORS.textSecondary, fontSize: '0.7rem' }}>
              Task Types
            </Typography>
          </CardContent>
        </Card>
        <Card sx={{ textAlign: 'center', bgcolor: `${OSO_COLORS.accent}10`, borderRadius: 1, flex: 1 }}>
          <CardContent sx={{ py: 1, px: 1.5 }}>
            <Typography variant="h6" sx={{ color: OSO_COLORS.accent, fontWeight: 600, fontSize: '1.25rem' }}>
              {totalStatuses}
            </Typography>
            <Typography variant="caption" sx={{ color: OSO_COLORS.textSecondary, fontSize: '0.7rem' }}>
              Statuses
            </Typography>
          </CardContent>
        </Card>
        <Card sx={{ textAlign: 'center', bgcolor: `${OSO_COLORS.success}10`, borderRadius: 1, flex: 1 }}>
          <CardContent sx={{ py: 1, px: 1.5 }}>
            <Typography variant="h6" sx={{ color: OSO_COLORS.success, fontWeight: 600, fontSize: '1.25rem' }}>
              {totalButtons}
            </Typography>
            <Typography variant="caption" sx={{ color: OSO_COLORS.textSecondary, fontSize: '0.7rem' }}>
              Actions
            </Typography>
          </CardContent>
        </Card>
        <Card sx={{ textAlign: 'center', bgcolor: `${OSO_COLORS.success}10`, borderRadius: 1, flex: 1 }}>
          <CardContent sx={{ py: 1, px: 1.5 }}>
            <CheckIcon sx={{ color: OSO_COLORS.success, fontSize: 20 }} />
            <Typography variant="caption" sx={{ color: OSO_COLORS.textSecondary, fontSize: '0.7rem', display: 'block' }}>
              Ready to Save
            </Typography>
          </CardContent>
        </Card>
      </Box>

      {/* Tabs for Preview and Code */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab icon={<PreviewIcon />} label="Policy Overview" />
          <Tab icon={<CodeIcon />} label="Generated Rego Code" />
        </Tabs>

        <TabPanel value={activeTab} index={0}>
          <Box sx={{ p: 3 }}>
            {/* Basic Info */}
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Policy Information
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Name
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {data.name}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Package
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {data.packageName}
                    </Typography>
                  </Grid>
                  {data.description && (
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="textSecondary">
                        Description
                      </Typography>
                      <Typography variant="body1">
                        {data.description}
                      </Typography>
                    </Grid>
                  )}
                </Grid>
              </CardContent>
            </Card>

            {/* Global Settings */}
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Global Settings
                </Typography>
                <Box display="flex" flexWrap="wrap" gap={1} mb={2}>
                  <Chip
                    label={`Add Condition Button: ${data.globalSettings.addConditionButton ? 'Enabled' : 'Disabled'}`}
                    color={data.globalSettings.addConditionButton ? 'success' : 'default'}
                    size="small"
                  />
                  <Chip
                    label={`User Type Check: ${data.globalSettings.allowUserTypeCheck ? 'Enabled' : 'Disabled'}`}
                    color={data.globalSettings.allowUserTypeCheck ? 'success' : 'default'}
                    size="small"
                  />
                </Box>
                {data.globalSettings.allowUserTypeCheck && (
                  <Box>
                    <Typography variant="subtitle2" gutterBottom>
                      Allowed User Types:
                    </Typography>
                    <Box display="flex" flexWrap="wrap" gap={0.5}>
                      {data.globalSettings.allowedUserTypes.map((type) => (
                        <Chip key={type} label={type} size="small" variant="outlined" />
                      ))}
                    </Box>
                  </Box>
                )}
              </CardContent>
            </Card>

            {/* Task Types Detail */}
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Task Types Configuration
                </Typography>
                {Object.entries(data.taskTypes).map(([taskTypeName, taskType]: [string, any]) => (
                  <Accordion key={taskTypeName} sx={{ mb: 1 }}>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography variant="subtitle1" fontWeight="bold">
                        {taskTypeName}
                      </Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      {Object.entries(taskType.status).map(([statusName, status]: [string, any]) => (
                        <Box key={statusName} sx={{ mb: 2 }}>
                          <Typography variant="subtitle2" color="primary" gutterBottom>
                            {statusName}
                          </Typography>
                          <Box display="flex" flexWrap="wrap" gap={1}>
                            {Object.entries(status.buttons).map(([buttonName, button]: [string, any]) => (
                              <Chip
                                key={buttonName}
                                label={`${buttonName}: ${getConditionLabel(button.condition)}`}
                                size="small"
                                variant="outlined"
                                color="primary"
                              />
                            ))}
                          </Box>
                        </Box>
                      ))}
                    </AccordionDetails>
                  </Accordion>
                ))}
              </CardContent>
            </Card>
          </Box>
        </TabPanel>

        <TabPanel value={activeTab} index={1}>
          <Box sx={{ p: 3 }}>
            <Alert severity="info" sx={{ mb: 2 }}>
              This is the Rego code that will be generated from your visual configuration.
              You don't need to understand this code - it's automatically created from your form inputs.
            </Alert>
            <Paper
              sx={{
                p: 2,
                bgcolor: 'grey.900',
                color: 'grey.100',
                fontFamily: 'monospace',
                fontSize: '0.875rem',
                overflow: 'auto',
                maxHeight: 400,
              }}
            >
              <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
                {generatedPolicy}
              </pre>
            </Paper>
          </Box>
        </TabPanel>
      </Paper>

      {/* Final Validation */}
      <Alert severity="success">
        <Typography variant="subtitle2" gutterBottom>
          Policy Ready for Deployment
        </Typography>
        <Typography variant="body2">
          Your policy has been successfully configured and is ready to be saved. 
          Click "Save Policy" to create this authorization policy in your system.
        </Typography>
      </Alert>
    </Box>
  );
};

export default PolicyPreviewStep;
