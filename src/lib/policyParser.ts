import { PolicyOutput, ParsedPolicyStructure, ButtonCondition, TaskType } from '@/types';

/**
 * Utility class for parsing and generating Rego policies with structured output
 */
export class PolicyParser {
  /**
   * Parse a Rego policy to extract structured output
   */
  static parsePolicyContent(content: string): ParsedPolicyStructure {
    try {
      // Look for output := { ... } pattern
      const outputMatch = content.match(/output\s*:=\s*(\{[\s\S]*?\n\})/);
      
      if (!outputMatch) {
        return {
          hasStructuredOutput: false,
          rawContent: content,
        };
      }

      const outputString = outputMatch[1];
      
      // Try to parse the output as JSON-like structure
      const parsedOutput = this.parseOutputObject(outputString);
      
      if (parsedOutput) {
        return {
          hasStructuredOutput: true,
          output: parsedOutput,
          rawContent: content,
        };
      }
    } catch (error) {
      console.warn('Failed to parse policy output:', error);
    }

    return {
      hasStructuredOutput: false,
      rawContent: content,
    };
  }

  /**
   * Parse the output object string into structured data
   */
  private static parseOutputObject(outputString: string): PolicyOutput | null {
    try {
      // Convert Rego-style object to JSON-parseable format
      let jsonString = outputString
        .replace(/(\w+):/g, '"$1":') // Quote keys
        .replace(/:\s*{/g, ': {') // Ensure proper spacing
        .replace(/},\s*}/g, '}}') // Fix trailing commas
        .replace(/,\s*}/g, '}'); // Remove trailing commas

      // Handle boolean values
      jsonString = jsonString.replace(/:\s*true/g, ': true');
      jsonString = jsonString.replace(/:\s*false/g, ': false');

      const parsed = JSON.parse(jsonString);
      
      // Validate structure
      if (parsed.taskTypes && parsed.buttons) {
        return parsed as PolicyOutput;
      }
    } catch (error) {
      console.warn('Failed to parse output object:', error);
    }
    
    return null;
  }

  /**
   * Generate Rego policy content from structured output
   */
  static generatePolicyContent(
    baseContent: string,
    structuredOutput: PolicyOutput
  ): string {
    const outputString = this.generateOutputObjectString(structuredOutput);
    
    // Replace existing output or add new one
    const outputPattern = /output\s*:=\s*\{[\s\S]*?\n\}/;
    
    if (outputPattern.test(baseContent)) {
      return baseContent.replace(outputPattern, `output := ${outputString}`);
    } else {
      // Add output at the end
      return `${baseContent}\n\noutput := ${outputString}`;
    }
  }

  /**
   * Generate the output object string from structured data
   */
  private static generateOutputObjectString(output: PolicyOutput): string {
    const taskTypesString = this.generateTaskTypesString(output.taskTypes);
    const buttonsString = this.generateButtonsString(output.buttons);

    return `{
\t"taskTypes": ${taskTypesString},
\t"buttons": ${buttonsString},
}`;
  }

  /**
   * Public method to generate output string (for use in visual builder)
   */
  static generateOutputString(output: PolicyOutput): string {
    return `output := ${this.generateOutputObjectString(output)}`;
  }

  /**
   * Generate taskTypes section
   */
  private static generateTaskTypesString(taskTypes: Record<string, any>): string {
    if (!taskTypes || Object.keys(taskTypes).length === 0) {
      return '{}';
    }

    const taskTypeEntries = Object.entries(taskTypes).map(([taskTypeName, taskType]) => {
      if (!taskType.status) {
        return `\t"${taskTypeName}": {"status": {}}`;
      }

      const statusEntries = Object.entries(taskType.status).map(([statusName, status]: [string, any]) => {
        if (!status.buttons) {
          return `\t\t\t"${statusName}": {"buttons": {}}`;
        }

        const buttonEntries = Object.entries(status.buttons).map(([buttonName, button]: [string, any]) => {
          const conditionString = this.generateConditionString(button.condition);
          return `\t\t\t\t"${buttonName}": {"condition": ${conditionString}}`;
        }).join(',\n');

        return `\t\t\t"${statusName}": {"buttons": {
${buttonEntries}
\t\t\t}}`;
      }).join(',\n');

      return `\t"${taskTypeName}": {"status": {
${statusEntries}
\t}}`;
    }).join(',\n');

    return `{\n${taskTypeEntries}\n}`;
  }

  /**
   * Generate buttons section
   */
  private static generateButtonsString(buttons: { addConditionButton: boolean }): string {
    return `{"addConditionButton": ${buttons.addConditionButton}}`;
  }

  /**
   * Generate condition string from condition object
   */
  private static generateConditionString(condition: ButtonCondition): string {
    if (condition.custom) {
      return condition.custom;
    }
    
    if (condition.createdBy) {
      return `{"createdBy": "${condition.createdBy}"}`;
    }
    
    return '{}';
  }

  /**
   * Get available condition options for UI
   */
  static getConditionOptions() {
    return [
      { value: 'currentUserAndAbove', label: 'Current User and Above' },
      { value: 'currentUser', label: 'Current User Only' },
      { value: 'AnyUser', label: 'Any User' },
      { value: 'custom', label: 'Custom Condition' },
    ];
  }

  /**
   * Get available button types
   */
  static getButtonTypes() {
    return [
      'delete',
      'update',
      'approve',
      'reject',
      'submit',
      'cancel',
      'view',
      'edit',
    ];
  }

  /**
   * Get available status types
   */
  static getStatusTypes() {
    return [
      'APPROVED',
      'UNSTARTED',
      'PENDING',
      'REJECTED',
      'COMPLETED',
      'IN_PROGRESS',
      'CANCELLED',
    ];
  }

  /**
   * Create a new empty task type structure
   */
  static createEmptyTaskType(): TaskType {
    return {
      status: {
        UNSTARTED: {
          buttons: {
            delete: { condition: { createdBy: 'currentUserAndAbove' as const } },
            update: { condition: { createdBy: 'currentUserAndAbove' as const } },
          },
        },
      },
    };
  }

  /**
   * Create a new empty policy output structure
   */
  static createEmptyPolicyOutput(): PolicyOutput {
    return {
      taskTypes: {},
      buttons: {
        addConditionButton: false,
      },
    };
  }
}
