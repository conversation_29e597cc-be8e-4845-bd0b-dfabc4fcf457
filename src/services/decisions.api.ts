import { api } from '@/lib/api';
import { DecisionLog, PaginatedResponse, DecisionLogFilters } from '@/types';
import { API_ENDPOINTS } from '@/constants';

export interface DecisionsQueryParams extends DecisionLogFilters {
  page?: number;
  limit?: number;
  sortBy?: 'timestamp' | 'duration' | 'system' | 'policy';
  sortOrder?: 'asc' | 'desc';
}

export interface DecisionTrends {
  totalDecisions: number;
  allowedDecisions: number;
  deniedDecisions: number;
  averageDuration: number;
  trendsData: {
    date: string;
    allowed: number;
    denied: number;
    avgDuration: number;
  }[];
}

export const decisionsApi = {
  // Get decision logs with filtering and pagination
  getDecisions: async (params?: DecisionsQueryParams): Promise<PaginatedResponse<DecisionLog>> => {
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.systemId) queryParams.append('systemId', params.systemId);
    if (params?.policyId) queryParams.append('policyId', params.policyId);
    if (params?.startDate) queryParams.append('startDate', params.startDate);
    if (params?.endDate) queryParams.append('endDate', params.endDate);
    if (params?.result !== undefined) queryParams.append('result', params.result.toString());
    if (params?.user) queryParams.append('user', params.user);
    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);
    
    const url = `${API_ENDPOINTS.DECISIONS}?${queryParams.toString()}`;
    return api.get<PaginatedResponse<DecisionLog>>(url);
  },

  // Get decision trends and analytics
  getDecisionTrends: async (
    startDate?: string,
    endDate?: string,
    systemId?: string
  ): Promise<DecisionTrends> => {
    const queryParams = new URLSearchParams();
    
    if (startDate) queryParams.append('startDate', startDate);
    if (endDate) queryParams.append('endDate', endDate);
    if (systemId) queryParams.append('systemId', systemId);
    
    const url = `${API_ENDPOINTS.DECISIONS_TRENDS}?${queryParams.toString()}`;
    return api.get<DecisionTrends>(url);
  },

  // Get decision by ID
  getDecisionById: async (id: string): Promise<DecisionLog> => {
    return api.get<DecisionLog>(`${API_ENDPOINTS.DECISIONS}/${id}`);
  },

  // Export decisions (for reporting)
  exportDecisions: async (params?: DecisionsQueryParams): Promise<Blob> => {
    const queryParams = new URLSearchParams();
    
    if (params?.systemId) queryParams.append('systemId', params.systemId);
    if (params?.policyId) queryParams.append('policyId', params.policyId);
    if (params?.startDate) queryParams.append('startDate', params.startDate);
    if (params?.endDate) queryParams.append('endDate', params.endDate);
    if (params?.result !== undefined) queryParams.append('result', params.result.toString());
    if (params?.user) queryParams.append('user', params.user);
    
    const url = `${API_ENDPOINTS.DECISIONS}/export?${queryParams.toString()}`;
    
    // Return blob for file download
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
      },
    });
    
    if (!response.ok) {
      throw new Error('Failed to export decisions');
    }
    
    return response.blob();
  },
};
