import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Chip,
  IconButton,
  Button,
  Menu,
  MenuItem,
  Tooltip,
  Divider,
  Stack,
  CardHeader,
  Paper,
  Collapse,
  Avatar,
  LinearProgress,
  Alert,
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  Security as SecurityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
} from '@mui/icons-material';
import { OSO_COLORS } from '@/styles/osoTheme';
import { PolicyParser } from '@/lib/policyParser';

interface PermissionCardProps {
  taskTypeName: string;
  taskType: any;
  onEditTaskType: (taskTypeName: string) => void;
  onDeleteTaskType: (taskTypeName: string) => void;
  onAddStatus: (taskTypeName: string) => void;
  onEditStatus: (taskTypeName: string, statusName: string) => void;
  onDeleteStatus: (taskTypeName: string, statusName: string) => void;
  onAddButton: (taskTypeName: string, statusName: string) => void;
  onEditButton: (taskTypeName: string, statusName: string, buttonName: string) => void;
  onDeleteButton: (taskTypeName: string, statusName: string, buttonName: string) => void;
}

const PermissionCard: React.FC<PermissionCardProps> = ({
  taskTypeName,
  taskType,
  onEditTaskType,
  onDeleteTaskType,
  onAddStatus,
  onDeleteStatus,
  onAddButton,
  onEditButton,
  onDeleteButton,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [expandedStatus, setExpandedStatus] = useState<string | null>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const getConditionLabel = (condition: any) => {
    if (condition.createdBy) {
      const options = PolicyParser.getConditionOptions();
      const option = options.find(opt => opt.value === condition.createdBy);
      return option?.label || condition.createdBy;
    }
    if (condition.custom) {
      return 'Custom';
    }
    return 'Unknown';
  };

  const getConditionColor = (condition: any) => {
    if (condition.createdBy === 'currentUserAndAbove') return 'warning';
    if (condition.createdBy === 'currentUser') return 'info';
    if (condition.createdBy === 'AnyUser') return 'success';
    return 'default';
  };

  const statusCount = Object.keys(taskType.status || {}).length;
  const totalButtons = Object.values(taskType.status || {}).reduce(
    (acc: number, status: any) => acc + Object.keys(status.buttons || {}).length,
    0
  );

  const getTaskTypeIcon = () => {
    // Return different icons based on task type name
    if (taskTypeName.includes('approval') || taskTypeName.includes('sanction')) {
      return <CheckCircleIcon />;
    }
    if (taskTypeName.includes('document') || taskTypeName.includes('content')) {
      return <InfoIcon />;
    }
    return <SecurityIcon />;
  };

  const getCompletionPercentage = () => {
    if (statusCount === 0) return 0;
    const configuredStatuses = Object.values(taskType.status || {}).filter(
      (status: any) => Object.keys(status.buttons || {}).length > 0
    ).length;
    return Math.round((configuredStatuses / statusCount) * 100);
  };

  const completionPercentage = getCompletionPercentage();

  return (
    <Card
      sx={{
        border: '1px solid #d1d9e0',
        borderRadius: '6px',
        boxShadow: 'none',
        backgroundColor: '#ffffff',
        '&:hover': {
          borderColor: '#0969da',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        },
        transition: 'all 0.2s ease-in-out',
      }}
    >
      {/* Compact Header */}
      <Box
        sx={{
          p: 2,
          pb: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <Box display="flex" alignItems="center" gap={1.5}>
          <Avatar
            sx={{
              bgcolor: '#0969da',
              color: 'white',
              width: 32,
              height: 32,
            }}
          >
            {getTaskTypeIcon()}
          </Avatar>
          <Box>
            <Typography
              variant="body2"
              sx={{
                fontWeight: 600,
                color: '#24292f',
                fontSize: '0.875rem',
                mb: 0.5,
              }}
            >
              {taskTypeName}
            </Typography>
            <Box display="flex" alignItems="center" gap={0.5}>
              <Chip
                size="small"
                label={`${statusCount} status${statusCount !== 1 ? 'es' : ''}`}
                sx={{
                  height: 18,
                  fontSize: '0.7rem',
                  backgroundColor: '#e7f3ff',
                  color: '#0969da',
                }}
              />
              <Chip
                size="small"
                label={`${totalButtons} permission${totalButtons !== 1 ? 's' : ''}`}
                sx={{
                  height: 18,
                  fontSize: '0.7rem',
                  backgroundColor: '#dcfce7',
                  color: '#16a34a',
                }}
              />
            </Box>
          </Box>
        </Box>
        <IconButton
          size="small"
          onClick={handleMenuOpen}
          sx={{
            color: '#656d76',
            '&:hover': { color: '#0969da', backgroundColor: '#f6f8fa' },
          }}
        >
          <MoreVertIcon fontSize="small" />
        </IconButton>
      </Box>

      <CardContent sx={{ pt: 0, px: 2, pb: 2 }}>
        {statusCount === 0 ? (
          <Box
            sx={{
              p: 1.5,
              textAlign: 'center',
              border: '1px dashed #d1d9e0',
              borderRadius: '4px',
              backgroundColor: '#f6f8fa',
            }}
          >
            <Typography variant="caption" sx={{ color: '#656d76', display: 'block', mb: 1 }}>
              No statuses configured yet
            </Typography>
            <Button
              size="small"
              startIcon={<AddIcon />}
              onClick={() => onAddStatus(taskTypeName)}
              sx={{
                textTransform: 'none',
                fontSize: '0.75rem',
                color: '#0969da',
                minWidth: 'auto',
                px: 1,
              }}
            >
              Add Status
            </Button>
          </Box>
        ) : (
          <Box>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
              <Typography variant="caption" sx={{ fontWeight: 600, color: '#24292f', fontSize: '0.75rem' }}>
                Statuses
              </Typography>
              <Button
                size="small"
                startIcon={<AddIcon />}
                onClick={() => onAddStatus(taskTypeName)}
                sx={{
                  textTransform: 'none',
                  fontSize: '0.7rem',
                  color: '#656d76',
                  minWidth: 'auto',
                  px: 1,
                  '&:hover': { color: '#0969da' },
                }}
              >
                Add
              </Button>
            </Box>

            {/* Compact Status List */}
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
              {Object.entries(taskType.status || {}).map(([statusName, status]: [string, any]) => {
                const buttonCount = Object.keys(status.buttons || {}).length;
                const isExpanded = expandedStatus === statusName;

                return (
                  <Box
                    key={statusName}
                    sx={{
                      border: '1px solid #d1d9e0',
                      borderRadius: '4px',
                      overflow: 'hidden',
                      backgroundColor: '#ffffff',
                      '&:hover': { borderColor: '#0969da' },
                    }}
                  >
                    {/* Compact Status Header */}
                    <Box
                      sx={{
                        p: 1,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        cursor: 'pointer',
                        backgroundColor: buttonCount > 0 ? '#dcfce7' : '#fef3c7',
                      }}
                      onClick={() => setExpandedStatus(isExpanded ? null : statusName)}
                    >
                      <Box display="flex" alignItems="center" gap={1}>
                        <Typography
                          variant="caption"
                          sx={{
                            fontWeight: 600,
                            color: '#24292f',
                            fontSize: '0.75rem',
                          }}
                        >
                          {statusName}
                        </Typography>
                        <Chip
                          size="small"
                          label={buttonCount}
                          sx={{
                            height: 16,
                            fontSize: '0.65rem',
                            backgroundColor: buttonCount > 0 ? '#16a34a' : '#f59e0b',
                            color: 'white',
                          }}
                        />
                      </Box>

                      <Box display="flex" alignItems="center" gap={0.5}>
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            onAddButton(taskTypeName, statusName);
                          }}
                          sx={{
                            color: '#656d76',
                            width: 24,
                            height: 24,
                            '&:hover': { color: '#0969da', backgroundColor: '#f6f8fa' },
                          }}
                        >
                          <AddIcon sx={{ fontSize: 14 }} />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                              onDeleteStatus(taskTypeName, statusName);
                          }}
                          sx={{
                            color: '#dc2626',
                            width: 24,
                            height: 24,
                            '&:hover': { color: '#dc2626', backgroundColor: '#fef2f2' },
                          }}
                        >
                          <DeleteIcon sx={{ fontSize: 14 }} />
                        </IconButton>
                        <IconButton
                          size="small"
                          sx={{
                            color: '#656d76',
                            width: 24,
                            height: 24,
                            '&:hover': { backgroundColor: '#f6f8fa' },
                          }}
                        >
                          {isExpanded ? <ExpandLessIcon sx={{ fontSize: 14 }} /> : <ExpandMoreIcon sx={{ fontSize: 14 }} />}
                        </IconButton>
                      </Box>
                    </Box>

                    {/* Compact Permissions List */}
                    <Collapse in={isExpanded}>
                      <Box sx={{ px: 1, pb: 1 }}>
                        {buttonCount === 0 ? (
                          <Typography
                            variant="caption"
                            sx={{ color: '#656d76', textAlign: 'center', display: 'block', py: 1 }}
                          >
                            No permissions configured
                          </Typography>
                        ) : (
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                            {Object.entries(status.buttons || {}).map(([buttonName, button]: [string, any]) => (
                              <Box
                                key={buttonName}
                                sx={{
                                  display: 'flex',
                                  justifyContent: 'space-between',
                                  alignItems: 'center',
                                  p: 0.75,
                                  backgroundColor: '#f6f8fa',
                                  borderRadius: '4px',
                                  border: '1px solid #d1d9e0',
                                }}
                              >
                                <Box display="flex" alignItems="center" gap={0.5}>
                                  <Chip
                                    label={buttonName}
                                    size="small"
                                    sx={{
                                      height: 16,
                                      fontSize: '0.65rem',
                                      backgroundColor: '#e7f3ff',
                                      color: '#0969da',
                                    }}
                                  />
                                  <Chip
                                    label={getConditionLabel(button.condition)}
                                    size="small"
                                    sx={{
                                      height: 16,
                                      fontSize: '0.65rem',
                                      backgroundColor: '#dcfce7',
                                      color: '#16a34a',
                                    }}
                                  />
                                </Box>

                                <Box display="flex" gap={0.25}>
                                  <IconButton
                                    size="small"
                                    onClick={() => onEditButton(taskTypeName, statusName, buttonName)}
                                    sx={{
                                      color: '#656d76',
                                      width: 20,
                                      height: 20,
                                      '&:hover': { color: '#0969da', backgroundColor: '#f6f8fa' },
                                    }}
                                  >
                                    <EditIcon sx={{ fontSize: 12 }} />
                                  </IconButton>
                                  <IconButton
                                    size="small"
                                    onClick={() => onDeleteButton(taskTypeName, statusName, buttonName)}
                                    sx={{
                                      color: '#656d76',
                                      width: 20,
                                      height: 20,
                                      '&:hover': { color: '#dc2626', backgroundColor: '#fef2f2' },
                                    }}
                                  >
                                    <DeleteIcon sx={{ fontSize: 12 }} />
                                  </IconButton>
                                </Box>
                              </Box>
                          ))}
                          </Box>
                        )}
                      </Box>
                    </Collapse>
                  </Box>
                );
              })}
            </Box>
          </Box>
        )}
      </CardContent>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          sx: {
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            border: '1px solid #d1d9e0',
          },
        }}
      >
        <MenuItem
          onClick={() => {
            onEditTaskType(taskTypeName);
            handleMenuClose();
          }}
          sx={{
            fontSize: '0.875rem',
            '&:hover': { backgroundColor: '#f6f8fa' },
          }}
        >
          <EditIcon fontSize="small" sx={{ mr: 1, color: '#0969da' }} />
          Edit Task Type
        </MenuItem>
        <MenuItem
          onClick={() => {
            onDeleteTaskType(taskTypeName);
            handleMenuClose();
          }}
          sx={{
            fontSize: '0.875rem',
            '&:hover': { backgroundColor: '#fef2f2' },
          }}
        >
          <DeleteIcon fontSize="small" sx={{ mr: 1, color: '#dc2626' }} />
          Delete Task Type
        </MenuItem>
      </Menu>
    </Card>
  );
};

export default PermissionCard;
