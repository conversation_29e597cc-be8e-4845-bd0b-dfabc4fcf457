import { useQuery } from '@tanstack/react-query';
import { dashboardApi } from '@/services/dashboard.api';

// Query Keys
export const DASHBOARD_QUERY_KEYS = {
  all: ['dashboard'] as const,
  summary: () => [...DASHBOARD_QUERY_KEYS.all, 'summary'] as const,
  metrics: () => [...DASHBOARD_QUERY_KEYS.all, 'metrics'] as const,
  systemsHealth: () => [...DASHBOARD_QUERY_KEYS.all, 'systems-health'] as const,
  policyPerformance: (limit?: number) => [...DASHBOARD_QUERY_KEYS.all, 'policy-performance', limit] as const,
  decisionTrends: (period?: string) => [...DASHBOARD_QUERY_KEYS.all, 'decision-trends', period] as const,
  topSystems: (limit?: number) => [...DASHBOARD_QUERY_KEYS.all, 'top-systems', limit] as const,
  alerts: (limit?: number) => [...DASHBOARD_QUERY_KEYS.all, 'alerts', limit] as const,
};

// Get dashboard summary
export const useDashboardSummary = () => {
  return useQuery({
    queryKey: DASHBOARD_QUERY_KEYS.summary(),
    queryFn: () => dashboardApi.getDashboardSummary(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });
};

// Get dashboard metrics
export const useDashboardMetrics = () => {
  return useQuery({
    queryKey: DASHBOARD_QUERY_KEYS.metrics(),
    queryFn: () => dashboardApi.getDashboardMetrics(),
    staleTime: 2 * 60 * 1000,
    refetchInterval: 5 * 60 * 1000,
  });
};

// Get system health metrics
export const useSystemHealthMetrics = () => {
  return useQuery({
    queryKey: DASHBOARD_QUERY_KEYS.systemsHealth(),
    queryFn: () => dashboardApi.getSystemHealthMetrics(),
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes
  });
};

// Get policy performance metrics
export const usePolicyPerformanceMetrics = (limit: number = 10) => {
  return useQuery({
    queryKey: DASHBOARD_QUERY_KEYS.policyPerformance(limit),
    queryFn: () => dashboardApi.getPolicyPerformanceMetrics(limit),
    staleTime: 5 * 60 * 1000,
  });
};

// Get decision volume trends
export const useDecisionVolumeTrends = (period: '24h' | '7d' | '30d' = '24h') => {
  return useQuery({
    queryKey: DASHBOARD_QUERY_KEYS.decisionTrends(period),
    queryFn: () => dashboardApi.getDecisionVolumeTrends(period),
    staleTime: 5 * 60 * 1000,
  });
};

// Get top systems by decisions
export const useTopSystemsByDecisions = (limit: number = 5) => {
  return useQuery({
    queryKey: DASHBOARD_QUERY_KEYS.topSystems(limit),
    queryFn: () => dashboardApi.getTopSystemsByDecisions(limit),
    staleTime: 5 * 60 * 1000,
  });
};

// Get recent alerts
export const useRecentAlerts = (limit: number = 10) => {
  return useQuery({
    queryKey: DASHBOARD_QUERY_KEYS.alerts(limit),
    queryFn: () => dashboardApi.getRecentAlerts(limit),
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes
  });
};
