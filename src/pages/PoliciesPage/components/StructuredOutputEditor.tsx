import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Switch,
  FormControlLabel,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
} from '@mui/icons-material';
import { PolicyOutput, ButtonCondition } from '@/types';
import { PolicyParser } from '@/lib/policyParser';

interface StructuredOutputEditorProps {
  output: PolicyOutput;
  onChange: (output: PolicyOutput) => void;
  readOnly?: boolean;
}

interface TaskTypeDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (taskTypeName: string) => void;
  existingNames: string[];
}

interface ButtonDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (buttonName: string, condition: ButtonCondition) => void;
  existingButtons: string[];
  initialButton?: string;
  initialCondition?: ButtonCondition;
}

const TaskTypeDialog: React.FC<TaskTypeDialogProps> = ({
  open,
  onClose,
  onSave,
  existingNames,
}) => {
  const [taskTypeName, setTaskTypeName] = useState('');
  const [error, setError] = useState('');

  const handleSave = () => {
    if (!taskTypeName.trim()) {
      setError('Task type name is required');
      return;
    }
    if (existingNames.includes(taskTypeName)) {
      setError('Task type name already exists');
      return;
    }
    onSave(taskTypeName);
    setTaskTypeName('');
    setError('');
    onClose();
  };

  const handleClose = () => {
    setTaskTypeName('');
    setError('');
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>Add Task Type</DialogTitle>
      <DialogContent>
        <TextField
          autoFocus
          margin="dense"
          label="Task Type Name"
          fullWidth
          variant="outlined"
          value={taskTypeName}
          onChange={(e) => setTaskTypeName(e.target.value)}
          error={!!error}
          helperText={error}
          placeholder="e.g., mfl-lap-sanction"
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Cancel</Button>
        <Button onClick={handleSave} variant="contained">
          Add
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const ButtonDialog: React.FC<ButtonDialogProps> = ({
  open,
  onClose,
  onSave,
  existingButtons,
  initialButton = '',
  initialCondition = { createdBy: 'currentUserAndAbove' },
}) => {
  const [buttonName, setButtonName] = useState(initialButton);
  const [conditionType, setConditionType] = useState(
    initialCondition.custom ? 'custom' : initialCondition.createdBy || 'currentUserAndAbove'
  );
  const [customCondition, setCustomCondition] = useState(initialCondition.custom || '');
  const [error, setError] = useState('');

  const handleSave = () => {
    if (!buttonName.trim()) {
      setError('Button name is required');
      return;
    }
    if (!initialButton && existingButtons.includes(buttonName)) {
      setError('Button name already exists');
      return;
    }

    const condition: ButtonCondition = conditionType === 'custom'
      ? { custom: customCondition }
      : { createdBy: conditionType as any };

    onSave(buttonName, condition);
    if (!initialButton) {
      setButtonName('');
      setConditionType('currentUserAndAbove');
      setCustomCondition('');
    }
    setError('');
    onClose();
  };

  const handleClose = () => {
    if (!initialButton) {
      setButtonName('');
      setConditionType('currentUserAndAbove');
      setCustomCondition('');
    }
    setError('');
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>{initialButton ? 'Edit Button' : 'Add Button'}</DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Button Type</InputLabel>
              <Select
                value={buttonName}
                onChange={(e) => setButtonName(e.target.value)}
                label="Button Type"
              >
                {PolicyParser.getButtonTypes().map((type) => (
                  <MenuItem key={type} value={type}>
                    {type}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Condition</InputLabel>
              <Select
                value={conditionType}
                onChange={(e) => setConditionType(e.target.value)}
                label="Condition"
              >
                {PolicyParser.getConditionOptions().map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          {conditionType === 'custom' && (
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Custom Condition (JSON/Rego)"
                multiline
                rows={3}
                value={customCondition}
                onChange={(e) => setCustomCondition(e.target.value)}
                placeholder='{"createdBy": "customRule"}'
              />
            </Grid>
          )}
        </Grid>
        {error && (
          <Typography color="error" variant="body2" sx={{ mt: 1 }}>
            {error}
          </Typography>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Cancel</Button>
        <Button onClick={handleSave} variant="contained">
          {initialButton ? 'Update' : 'Add'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const StructuredOutputEditor: React.FC<StructuredOutputEditorProps> = ({
  output,
  onChange,
  readOnly = false,
}) => {
  const [taskTypeDialogOpen, setTaskTypeDialogOpen] = useState(false);
  const [buttonDialogOpen, setButtonDialogOpen] = useState(false);
  const [selectedTaskType, setSelectedTaskType] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [editingButton, setEditingButton] = useState<{
    name: string;
    condition: ButtonCondition;
  } | null>(null);

  const handleAddTaskType = (taskTypeName: string) => {
    const newOutput = {
      ...output,
      taskTypes: {
        ...output.taskTypes,
        [taskTypeName]: PolicyParser.createEmptyTaskType(),
      },
    };
    onChange(newOutput);
  };

  const handleDeleteTaskType = (taskTypeName: string) => {
    const newTaskTypes = { ...output.taskTypes };
    delete newTaskTypes[taskTypeName];
    onChange({
      ...output,
      taskTypes: newTaskTypes,
    });
  };

  const handleAddStatus = (taskTypeName: string, statusName: string) => {
    const newOutput = {
      ...output,
      taskTypes: {
        ...output.taskTypes,
        [taskTypeName]: {
          ...output.taskTypes[taskTypeName],
          status: {
            ...output.taskTypes[taskTypeName].status,
            [statusName]: {
              buttons: {},
            },
          },
        },
      },
    };
    onChange(newOutput);
  };

  const handleDeleteStatus = (taskTypeName: string, statusName: string) => {
    const newStatus = { ...output.taskTypes[taskTypeName].status };
    delete newStatus[statusName];
    onChange({
      ...output,
      taskTypes: {
        ...output.taskTypes,
        [taskTypeName]: {
          ...output.taskTypes[taskTypeName],
          status: newStatus,
        },
      },
    });
  };

  const handleAddButton = (buttonName: string, condition: ButtonCondition) => {
    const newOutput = {
      ...output,
      taskTypes: {
        ...output.taskTypes,
        [selectedTaskType]: {
          ...output.taskTypes[selectedTaskType],
          status: {
            ...output.taskTypes[selectedTaskType].status,
            [selectedStatus]: {
              ...output.taskTypes[selectedTaskType].status[selectedStatus],
              buttons: {
                ...output.taskTypes[selectedTaskType].status[selectedStatus].buttons,
                [buttonName]: { condition },
              },
            },
          },
        },
      },
    };
    onChange(newOutput);
  };

  const handleEditButton = (buttonName: string, condition: ButtonCondition) => {
    handleAddButton(buttonName, condition);
    setEditingButton(null);
  };

  const handleDeleteButton = (taskTypeName: string, statusName: string, buttonName: string) => {
    const newButtons = { ...output.taskTypes[taskTypeName].status[statusName].buttons };
    delete newButtons[buttonName];
    onChange({
      ...output,
      taskTypes: {
        ...output.taskTypes,
        [taskTypeName]: {
          ...output.taskTypes[taskTypeName],
          status: {
            ...output.taskTypes[taskTypeName].status,
            [statusName]: {
              ...output.taskTypes[taskTypeName].status[statusName],
              buttons: newButtons,
            },
          },
        },
      },
    });
  };

  const handleGlobalButtonToggle = (checked: boolean) => {
    onChange({
      ...output,
      buttons: {
        ...output.buttons,
        addConditionButton: checked,
      },
    });
  };

  const openButtonDialog = (taskTypeName: string, statusName: string) => {
    setSelectedTaskType(taskTypeName);
    setSelectedStatus(statusName);
    setButtonDialogOpen(true);
  };

  const openEditButtonDialog = (
    taskTypeName: string,
    statusName: string,
    buttonName: string,
    condition: ButtonCondition
  ) => {
    setSelectedTaskType(taskTypeName);
    setSelectedStatus(statusName);
    setEditingButton({ name: buttonName, condition });
    setButtonDialogOpen(true);
  };

  return (
    <Box sx={{ p: 3, maxHeight: '500px', overflow: 'auto' }}>
      <Typography variant="h6" gutterBottom>
        Output Configuration
      </Typography>
      <Typography variant="body2" color="textSecondary" paragraph>
        Configure the structured output for your policy. This defines task types, their statuses,
        and the buttons available for each status.
      </Typography>

      {/* Global Buttons Section */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Global Settings
          </Typography>
          <FormControlLabel
            control={
              <Switch
                checked={output.buttons.addConditionButton}
                onChange={(e) => handleGlobalButtonToggle(e.target.checked)}
                disabled={readOnly}
              />
            }
            label="Add Condition Button"
          />
        </CardContent>
      </Card>

      {/* Task Types Section */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h6">Task Types</Typography>
        {!readOnly && (
          <Button
            variant="outlined"
            startIcon={<AddIcon />}
            onClick={() => setTaskTypeDialogOpen(true)}
          >
            Add Task Type
          </Button>
        )}
      </Box>

      {Object.entries(output.taskTypes).map(([taskTypeName, taskType]) => (
        <Accordion key={taskTypeName} sx={{ mb: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box display="flex" justifyContent="space-between" alignItems="center" width="100%">
              <Typography variant="subtitle1" fontWeight="bold">
                {taskTypeName}
              </Typography>
              {!readOnly && (
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteTaskType(taskTypeName);
                  }}
                  color="error"
                >
                  <DeleteIcon />
                </IconButton>
              )}
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            {Object.entries(taskType.status).map(([statusName, status]) => (
              <Card key={statusName} variant="outlined" sx={{ mb: 2 }}>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Typography variant="h6" color="primary">
                      {statusName}
                    </Typography>
                    {!readOnly && (
                      <IconButton
                        size="small"
                        onClick={() => handleDeleteStatus(taskTypeName, statusName)}
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    )}
                  </Box>

                  <Typography variant="subtitle2" gutterBottom>
                    Buttons:
                  </Typography>

                  <Box display="flex" flexWrap="wrap" gap={1} mb={2}>
                    {Object.entries(status.buttons).map(([buttonName, button]) => (
                      <Chip
                        key={buttonName}
                        label={`${buttonName}: ${
                          button.condition.custom || button.condition.createdBy || 'No condition'
                        }`}
                        onDelete={
                          readOnly
                            ? undefined
                            : () => handleDeleteButton(taskTypeName, statusName, buttonName)
                        }
                        onClick={
                          readOnly
                            ? undefined
                            : () =>
                                openEditButtonDialog(
                                  taskTypeName,
                                  statusName,
                                  buttonName,
                                  button.condition
                                )
                        }
                        color="primary"
                        variant="outlined"
                      />
                    ))}
                  </Box>

                  {!readOnly && (
                    <Button
                      size="small"
                      startIcon={<AddIcon />}
                      onClick={() => openButtonDialog(taskTypeName, statusName)}
                    >
                      Add Button
                    </Button>
                  )}
                </CardContent>
              </Card>
            ))}

            {!readOnly && (
              <Button
                variant="outlined"
                size="small"
                onClick={() => {
                  const statusName = prompt('Enter status name:');
                  if (statusName) {
                    handleAddStatus(taskTypeName, statusName);
                  }
                }}
              >
                Add Status
              </Button>
            )}
          </AccordionDetails>
        </Accordion>
      ))}

      {/* Dialogs */}
      <TaskTypeDialog
        open={taskTypeDialogOpen}
        onClose={() => setTaskTypeDialogOpen(false)}
        onSave={handleAddTaskType}
        existingNames={Object.keys(output.taskTypes)}
      />

      <ButtonDialog
        open={buttonDialogOpen}
        onClose={() => {
          setButtonDialogOpen(false);
          setEditingButton(null);
        }}
        onSave={editingButton ? handleEditButton : handleAddButton}
        existingButtons={
          selectedTaskType && selectedStatus
            ? Object.keys(output.taskTypes[selectedTaskType]?.status[selectedStatus]?.buttons || {})
            : []
        }
        initialButton={editingButton?.name}
        initialCondition={editingButton?.condition}
      />
    </Box>
  );
};

export default StructuredOutputEditor;
