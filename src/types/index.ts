// Core API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// System/Application Types
export interface System {
  id: string;
  name: string;
  description?: string;
  type: 'web' | 'api' | 'service' | 'database';
  status: 'active' | 'inactive' | 'maintenance';
  url?: string;
  createdAt: string;
  updatedAt: string;
  policies?: Policy[];
}

// Financial Product Types
export interface FinancialProduct {
  id: string;
  name: string;
  type: 'web' | 'api' | 'service' | 'platform';
  description: string;
  icon: string;
  color: string;
  status?: 'active' | 'inactive' | 'maintenance';
  policies?: number;
  decisions24h?: number;
  uptime?: number;
}

// Policy Types
export interface Policy {
  id: string;
  name: string;
  description?: string;
  content: string; // Rego policy content
  version: string;
  productId: string; // Changed from systemId to productId
  status: 'draft' | 'active' | 'deprecated';
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  tags?: string[];
  permissions?: PolicyPermission[];
}

// Policy Permission Types
export interface PolicyPermission {
  id: string;
  name: string;
  action: string;
  resource: string;
  condition: ButtonCondition;
  description?: string;
}

// Structured Output Types for Policy Editor
export interface ButtonCondition {
  createdBy?: 'currentUserAndAbove' | 'AnyUser' | 'currentUser';
  custom?: string; // For custom Rego conditions
}

export interface PolicyButton {
  condition: ButtonCondition;
}

export interface TaskTypeStatus {
  buttons: Record<string, PolicyButton>;
}

export interface TaskType {
  status: Record<string, TaskTypeStatus>;
}

export interface PolicyOutput {
  taskTypes: Record<string, TaskType>;
  buttons: {
    addConditionButton: boolean;
  };
}

export interface ParsedPolicyStructure {
  hasStructuredOutput: boolean;
  output?: PolicyOutput;
  rawContent: string;
}

export interface PolicyVersion {
  id: string;
  policyId: string;
  version: string;
  content: string;
  createdAt: string;
  createdBy: string;
  isActive: boolean;
}

// Decision Log Types
export interface DecisionLog {
  id: string;
  timestamp: string;
  systemId: string;
  policyId: string;
  input: Record<string, any>;
  result: {
    allow: boolean;
    reason?: string;
    metadata?: Record<string, any>;
  };
  duration: number; // in milliseconds
  user?: string;
}

// Data Bundle Types
export interface DataBundle {
  id: string;
  name: string;
  description?: string;
  content: Record<string, any>;
  version: string;
  systemId?: string;
  createdAt: string;
  updatedAt: string;
}

// User & Auth Types
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'editor' | 'viewer';
  createdAt: string;
  lastLogin?: string;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

// Dashboard/Metrics Types
export interface DashboardMetrics {
  totalSystems: number;
  totalPolicies: number;
  totalDecisions: number;
  recentDecisions: DecisionLog[];
  systemsHealth: {
    healthy: number;
    warning: number;
    critical: number;
  };
  policyDistribution: {
    active: number;
    draft: number;
    deprecated: number;
  };
}

// API Configuration
export interface ApiConfig {
  baseURL: string;
  timeout: number;
  retries: number;
}

// Form Types
export interface CreateSystemForm {
  name: string;
  description?: string;
  type: System['type'];
  url?: string;
}

export interface CreatePolicyForm {
  name: string;
  description?: string;
  content: string;
  systemId: string;
  tags?: string[];
}

// Filter and Search Types
export interface DecisionLogFilters {
  systemId?: string;
  policyId?: string;
  startDate?: string;
  endDate?: string;
  result?: boolean;
  user?: string;
}

export interface PolicyFilters {
  systemId?: string;
  status?: Policy['status'];
  tags?: string[];
  search?: string;
}

// UI State Types
export interface UIState {
  theme: 'light' | 'dark';
  sidebarOpen: boolean;
  loading: boolean;
  error: string | null;
}
