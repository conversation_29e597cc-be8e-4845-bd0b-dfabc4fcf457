import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Grid,
  Card,
  CardContent,
  CardActions,
  Chip,
  IconButton,
  Dialog,
  DialogContent,
  DialogTitle,
  DialogActions,
  Alert,
  Snackbar,
  Avatar,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Container,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreIcon,
  Visibility as ViewIcon,
  ContentCopy as CopyIcon,
  Dashboard as DashboardIcon,
  Security as SecurityIcon,
  AccountBalance as BankIcon,
  DeviceHub as DeviceIcon,
} from '@mui/icons-material';
import VisualPolicyBuilder from './components/VisualPolicyBuilder';
import { POLICY_MANAGEMENT_PRODUCTS } from '@/constants';
import { Policy } from '@/types';

// Sample policy content with structured output
const samplePolicyContent = `package policy

user_type_allowed(allowed_user_types) if input.user.type in allowed_user_types

else := false

output := {
\t"taskTypes": {"mfl-lap-sanction": {"status": {
\t\t"APPROVED": {"buttons": {
\t\t\t"delete": {"condition": {"createdBy": "currentUserAndAbove"}},
\t\t\t"update": {"condition": {"createdBy": "currentUserAndAbove"}},
\t\t}},
\t\t"UNSTARTED": {"buttons": {
\t\t\t"delete": {"condition": {"createdBy": "currentUserAndAbove"}},
\t\t\t"update": {"condition": {"createdBy": "currentUserAndAbove"}},
\t\t}},
\t\t"PENDING": {"buttons": {
\t\t\t"delete": {"condition": {"createdBy": "currentUserAndAbove"}},
\t\t\t"update": {"condition": {"createdBy": "currentUserAndAbove"}},
\t\t}},
\t}}},
\t"buttons": {"addConditionButton": false},
}`;

// Mock data with financial products (limited to Lender Dashboard and Platform Dashboard)
const mockPolicies: Policy[] = [
  {
    id: '1',
    name: 'Lender Dashboard',
    description: 'Web application for comprehensive lending management platform',
    content: samplePolicyContent,
    version: '1.0.0',
    productId: 'lender-dashboard',
    status: 'active',
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-20T14:45:00Z',
    createdBy: '<EMAIL>',
    tags: ['web', 'lending', 'dashboard'],
    permissions: [
      {
        id: 'perm-1',
        name: 'access_dashboard',
        action: 'read',
        resource: 'lender-dashboard',
        condition: { createdBy: 'currentUser' },
        description: 'Access lender dashboard interface',
      },
      {
        id: 'perm-2',
        name: 'manage_loans',
        action: 'write',
        resource: 'loan-applications',
        condition: { createdBy: 'currentUserAndAbove' },
        description: 'Manage loan applications and processes',
      },
    ],
  },
  {
    id: '2',
    name: 'Platform Dashboard',
    description: 'Web application for central platform management and operations',
    content: 'package platform_dashboard\n\nallow if input.user.role == "admin"\nallow if input.user.department == "operations"',
    version: '1.3.0',
    productId: 'platform-dashboard',
    status: 'active',
    createdAt: '2024-01-10T09:15:00Z',
    updatedAt: '2024-01-18T16:20:00Z',
    createdBy: '<EMAIL>',
    tags: ['web', 'platform', 'dashboard'],
    permissions: [
      {
        id: 'perm-3',
        name: 'access_platform',
        action: 'read',
        resource: 'platform-dashboard',
        condition: { createdBy: 'currentUser' },
        description: 'Access platform dashboard interface',
      },
      {
        id: 'perm-4',
        name: 'manage_platform',
        action: 'write',
        resource: 'platform-settings',
        condition: { createdBy: 'currentUser' },
        description: 'Manage platform configuration and settings',
      },
    ],
  },
];



const getProductIcon = (productId: string) => {
  const product = POLICY_MANAGEMENT_PRODUCTS.find(p => p.id === productId);
  if (!product) return <DashboardIcon />;

  switch (product.icon) {
    case 'dashboard':
      return <DashboardIcon />;
    case 'security':
      return <SecurityIcon />;
    case 'bank':
      return <BankIcon />;
    case 'device':
      return <DeviceIcon />;
    default:
      return <DashboardIcon />;
  }
};

const getProductInfo = (productId: string) => {
  return POLICY_MANAGEMENT_PRODUCTS.find(p => p.id === productId);
};

const PoliciesPage: React.FC = () => {
  const [policies, setPolicies] = useState<Policy[]>(mockPolicies);
  const [editorOpen, setEditorOpen] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<Policy | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [policyToDelete, setPolicyToDelete] = useState<Policy | null>(null);
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [menuPolicy, setMenuPolicy] = useState<Policy | null>(null);
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'success',
  });

  // CRUD Operations
  const handleCreatePolicy = () => {
    setSelectedPolicy(null);
    setEditorOpen(true);
  };

  const handleEditPolicy = (policy: Policy) => {
    setSelectedPolicy(policy);
    setEditorOpen(true);
  };

  const handleViewPolicy = (policy: Policy) => {
    // TODO: Implement policy view modal
    console.log('Viewing policy:', policy);
  };

  const handleCopyPolicy = (policy: Policy) => {
    try {
      const newPolicy: Policy = {
        ...policy,
        id: `${Date.now()}`,
        name: `${policy.name} (Copy)`,
        status: 'draft',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: '1.0.0',
      };
      setPolicies(prev => [...prev, newPolicy]);
      setSnackbar({
        open: true,
        message: 'Policy copied successfully',
        severity: 'success',
      });
    } catch (error) {
      console.error('Error copying policy:', error);
      setSnackbar({
        open: true,
        message: 'Failed to copy policy. Please try again.',
        severity: 'error',
      });
    }
  };

  const handleDeletePolicy = (policy: Policy) => {
    setPolicyToDelete(policy);
    setDeleteDialogOpen(true);
  };

  const confirmDeletePolicy = () => {
    try {
      if (policyToDelete) {
        setPolicies(prev => prev.filter(p => p.id !== policyToDelete.id));
        setSnackbar({
          open: true,
          message: 'Policy deleted successfully',
          severity: 'success',
        });
      }
    } catch (error) {
      console.error('Error deleting policy:', error);
      setSnackbar({
        open: true,
        message: 'Failed to delete policy. Please try again.',
        severity: 'error',
      });
    } finally {
      setDeleteDialogOpen(false);
      setPolicyToDelete(null);
    }
  };

  const handleSavePolicy = (policyData: {
    name: string;
    description: string;
    content: string;
    productId?: string;
    permissions?: any[];
  }) => {
    try {
      // Validate required fields
      if (!policyData.name?.trim()) {
        setSnackbar({
          open: true,
          message: 'Policy name is required',
          severity: 'error',
        });
        return;
      }

      if (!policyData.productId?.trim()) {
        setSnackbar({
          open: true,
          message: 'Product selection is required',
          severity: 'error',
        });
        return;
      }

      if (selectedPolicy) {
        // Update existing policy
        setPolicies(prev => prev.map(p =>
          p.id === selectedPolicy.id
            ? {
                ...p,
                ...policyData,
                updatedAt: new Date().toISOString(),
                version: incrementVersion(p.version),
              }
            : p
        ));
        setSnackbar({
          open: true,
          message: 'Policy updated successfully',
          severity: 'success',
        });
      } else {
        // Create new policy
        const newPolicy: Policy = {
          id: `${Date.now()}`,
          name: policyData.name,
          description: policyData.description,
          content: policyData.content,
          productId: policyData.productId || '',
          version: '1.0.0',
          status: 'draft',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          createdBy: '<EMAIL>',
          tags: [],
          permissions: policyData.permissions || [],
        };
        setPolicies(prev => [...prev, newPolicy]);
        setSnackbar({
          open: true,
          message: 'Policy created successfully',
          severity: 'success',
        });
      }
      setEditorOpen(false);
      setSelectedPolicy(null);
    } catch (error) {
      console.error('Error saving policy:', error);
      setSnackbar({
        open: true,
        message: `Failed to ${selectedPolicy ? 'update' : 'create'} policy. Please try again.`,
        severity: 'error',
      });
    }
  };

  const handleCancelEdit = () => {
    setEditorOpen(false);
    setSelectedPolicy(null);
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, policy: Policy) => {
    setMenuAnchorEl(event.currentTarget);
    setMenuPolicy(policy);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
    setMenuPolicy(null);
  };

  const incrementVersion = (version: string): string => {
    const parts = version.split('.');
    const patch = parseInt(parts[2] || '0') + 1;
    return `${parts[0]}.${parts[1]}.${patch}`;
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* Header Section */}
      <Box sx={{ mb: 4 }}>
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems={{ xs: 'flex-start', sm: 'center' }}
          flexDirection={{ xs: 'column', sm: 'row' }}
          gap={{ xs: 2, sm: 0 }}
          mb={3}
        >
          <Box>
            <Typography
              variant="h4"
              component="h1"
              sx={{
                fontWeight: 600,
                fontSize: { xs: '1.5rem', sm: '1.75rem' },
                color: '#24292f',
                mb: 1,
                lineHeight: 1.2
              }}
            >
              Policy Management
            </Typography>
            <Typography
              variant="body1"
              sx={{
                color: '#656d76',
                fontSize: '1rem',
                maxWidth: '600px'
              }}
            >
              Create and manage authorization policies for your financial products
            </Typography>
          </Box>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreatePolicy}
            sx={{
              backgroundColor: '#1f883d',
              color: 'white',
              fontWeight: 600,
              textTransform: 'none',
              borderRadius: '6px',
              px: 3,
              py: 1.5,
              fontSize: '0.875rem',
              boxShadow: 'none',
              alignSelf: { xs: 'flex-start', sm: 'auto' },
              '&:hover': {
                backgroundColor: '#1a7f37',
                boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
              },
            }}
          >
            Create Policy
          </Button>
        </Box>

        {/* Stats Section */}
        <Box
          sx={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: 3,
            py: 2,
            px: 3,
            backgroundColor: '#f6f8fa',
            borderRadius: '8px',
            border: '1px solid #d1d9e0'
          }}
        >
          <Box display="flex" alignItems="center" gap={1}>
            <Typography variant="body2" sx={{ color: '#656d76', fontSize: '0.875rem' }}>
              Total Policies:
            </Typography>
            <Typography variant="body2" sx={{ color: '#24292f', fontWeight: 600, fontSize: '0.875rem' }}>
              {policies.length}
            </Typography>
          </Box>
          <Box display="flex" alignItems="center" gap={1}>
            <Typography variant="body2" sx={{ color: '#656d76', fontSize: '0.875rem' }}>
              Active:
            </Typography>
            <Typography variant="body2" sx={{ color: '#1f883d', fontWeight: 600, fontSize: '0.875rem' }}>
              {policies.filter(p => p.status === 'active').length}
            </Typography>
          </Box>
          <Box display="flex" alignItems="center" gap={1}>
            <Typography variant="body2" sx={{ color: '#656d76', fontSize: '0.875rem' }}>
              Draft:
            </Typography>
            <Typography variant="body2" sx={{ color: '#0969da', fontWeight: 600, fontSize: '0.875rem' }}>
              {policies.filter(p => p.status === 'draft').length}
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Policy Cards Section */}
      <Box>
        <Typography
          variant="h6"
          sx={{
            color: '#24292f',
            fontWeight: 600,
            mb: 3,
            fontSize: '1.125rem'
          }}
        >
          Policies ({policies.length})
        </Typography>

        <Grid container spacing={3}>
          {policies.map((policy) => {
            const product = getProductInfo(policy.productId);
            return (
              <Grid item xs={12} md={6} lg={4} key={policy.id}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    border: '1px solid #d1d9e0',
                    borderRadius: '8px',
                    boxShadow: 'none',
                    backgroundColor: '#ffffff',
                    '&:hover': {
                      borderColor: '#0969da',
                      boxShadow: '0 3px 12px rgba(0,0,0,0.1)',
                      transform: 'translateY(-1px)',
                    },
                    transition: 'all 0.2s ease-in-out',
                  }}
                >
                <CardContent sx={{ flexGrow: 1, p: 3 }}>
                  {/* Header with Product Info */}
                  <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                    <Box display="flex" alignItems="center" gap={1.5} flex={1}>
                      {product && (
                        <Avatar
                          sx={{
                            width: 32,
                            height: 32,
                            backgroundColor: product.color,
                            color: 'white',
                          }}
                        >
                          {getProductIcon(policy.productId)}
                        </Avatar>
                      )}
                      <Box flex={1}>
                        <Typography
                          variant="h6"
                          sx={{
                            fontWeight: 600,
                            fontSize: '1rem',
                            color: '#24292f',
                            mb: 0.5,
                          }}
                        >
                          {policy.name}
                        </Typography>
                        {product && (
                          <Typography
                            variant="caption"
                            sx={{
                              color: '#656d76',
                              fontSize: '0.75rem',
                            }}
                          >
                            {product.name} • {product.type}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Chip
                        label={policy.status.toUpperCase()}
                        size="small"
                        sx={{
                          height: 20,
                          fontSize: '0.7rem',
                          fontWeight: 600,
                          backgroundColor:
                            policy.status === 'active' ? '#dcfce7' :
                            policy.status === 'draft' ? '#dbeafe' : '#fef2f2',
                          color:
                            policy.status === 'active' ? '#16a34a' :
                            policy.status === 'draft' ? '#2563eb' : '#dc2626',
                        }}
                      />
                      <Tooltip title="More options">
                        <IconButton
                          size="small"
                          onClick={(e) => handleMenuOpen(e, policy)}
                          sx={{
                            color: '#656d76',
                            '&:hover': { color: '#0969da', backgroundColor: '#f6f8fa' },
                          }}
                        >
                          <MoreIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </Box>

                  {/* Description */}
                  <Typography
                    variant="body2"
                    sx={{
                      color: '#656d76',
                      fontSize: '0.875rem',
                      lineHeight: 1.5,
                      mb: 2,
                    }}
                  >
                    {policy.description}
                  </Typography>

                  {/* Metadata */}
                  <Box sx={{ mb: 2 }}>
                    <Typography
                      variant="caption"
                      sx={{
                        color: '#656d76',
                        fontSize: '0.75rem',
                        display: 'block',
                        mb: 0.5,
                      }}
                    >
                      Version: {policy.version} • Created: {new Date(policy.createdAt).toLocaleDateString()}
                    </Typography>
                    <Typography
                      variant="caption"
                      sx={{
                        color: '#656d76',
                        fontSize: '0.75rem',
                      }}
                    >
                      By: {policy.createdBy}
                    </Typography>
                  </Box>

                  {/* Tags */}
                  <Box display="flex" gap={0.5} flexWrap="wrap" mb={2}>
                    {policy.tags && policy.tags.map((tag) => (
                      <Chip
                        key={tag}
                        label={tag}
                        size="small"
                        sx={{
                          height: 18,
                          fontSize: '0.7rem',
                          backgroundColor: '#f6f8fa',
                          color: '#656d76',
                          border: '1px solid #d1d9e0',
                          '&:hover': { backgroundColor: '#e7f3ff' },
                        }}
                      />
                    ))}
                  </Box>

                  {/* Permissions Count */}
                  {policy.permissions && policy.permissions.length > 0 && (
                    <Typography
                      variant="caption"
                      sx={{
                        color: '#0969da',
                        fontSize: '0.75rem',
                        fontWeight: 500,
                      }}
                    >
                      {policy.permissions.length} permission{policy.permissions.length !== 1 ? 's' : ''} defined
                    </Typography>
                  )}
                </CardContent>

                {/* Action Buttons */}
                <CardActions sx={{ px: 3, pb: 3, pt: 0 }}>
                  <Box display="flex" gap={1} width="100%">
                    <Button
                      size="small"
                      variant="outlined"
                      startIcon={<EditIcon />}
                      onClick={() => handleEditPolicy(policy)}
                      sx={{
                        flex: 1,
                        borderColor: '#d1d9e0',
                        color: '#24292f',
                        fontSize: '0.75rem',
                        textTransform: 'none',
                        '&:hover': {
                          borderColor: '#0969da',
                          backgroundColor: '#f6f8fa',
                        },
                      }}
                    >
                      Edit
                    </Button>
                    <Button
                      size="small"
                      variant="outlined"
                      startIcon={<ViewIcon />}
                      onClick={() => handleViewPolicy(policy)}
                      sx={{
                        flex: 1,
                        borderColor: '#d1d9e0',
                        color: '#24292f',
                        fontSize: '0.75rem',
                        textTransform: 'none',
                        '&:hover': {
                          borderColor: '#0969da',
                          backgroundColor: '#f6f8fa',
                        },
                      }}
                    >
                      View
                    </Button>
                  </Box>
                </CardActions>
              </Card>
            </Grid>
          );
        })}
        </Grid>
      </Box>

      {/* Action Menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          sx: {
            border: '1px solid #d1d9e0',
            borderRadius: '6px',
            boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
            minWidth: 180,
          },
        }}
      >
        <MenuItem onClick={() => { handleEditPolicy(menuPolicy!); handleMenuClose(); }}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText primary="Edit Policy" />
        </MenuItem>
        <MenuItem onClick={() => { handleViewPolicy(menuPolicy!); handleMenuClose(); }}>
          <ListItemIcon>
            <ViewIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText primary="View Details" />
        </MenuItem>
        <MenuItem onClick={() => { handleCopyPolicy(menuPolicy!); handleMenuClose(); }}>
          <ListItemIcon>
            <CopyIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText primary="Duplicate" />
        </MenuItem>
        <Divider />
        <MenuItem
          onClick={() => { handleDeletePolicy(menuPolicy!); handleMenuClose(); }}
          sx={{ color: '#da3633' }}
        >
          <ListItemIcon>
            <DeleteIcon fontSize="small" sx={{ color: '#da3633' }} />
          </ListItemIcon>
          <ListItemText primary="Delete Policy" />
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        PaperProps={{
          sx: {
            borderRadius: '8px',
            maxWidth: '400px',
          },
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Typography variant="h6" sx={{ fontWeight: 600, color: '#24292f' }}>
            Delete Policy
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Typography sx={{ color: '#656d76', mb: 2 }}>
            Are you sure you want to delete "{policyToDelete?.name}"? This action cannot be undone.
          </Typography>
          {policyToDelete && (
            <Alert severity="warning" sx={{ borderRadius: '6px' }}>
              This will permanently delete the policy and all its associated permissions.
            </Alert>
          )}
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={() => setDeleteDialogOpen(false)}
            sx={{
              color: '#656d76',
              textTransform: 'none',
              '&:hover': { backgroundColor: '#f6f8fa' },
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={confirmDeletePolicy}
            variant="contained"
            sx={{
              backgroundColor: '#da3633',
              color: 'white',
              textTransform: 'none',
              '&:hover': { backgroundColor: '#b91c1c' },
            }}
          >
            Delete Policy
          </Button>
        </DialogActions>
      </Dialog>

      {/* Success/Error Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ borderRadius: '6px' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* Visual Policy Builder Dialog */}
      <Dialog
        open={editorOpen}
        onClose={handleCancelEdit}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: {
            height: '90vh',
            borderRadius: '8px',
          }
        }}
      >
        <DialogContent sx={{ p: 0 }}>
          <VisualPolicyBuilder
            initialPolicy={selectedPolicy ? {
              name: selectedPolicy.name,
              description: selectedPolicy.description || '',
              content: selectedPolicy.content,
              productId: selectedPolicy.productId,
              permissions: selectedPolicy.permissions || [],
            } : undefined}
            onSave={handleSavePolicy}
            onCancel={handleCancelEdit}
          />
        </DialogContent>
      </Dialog>
    </Container>
  );
};

export default PoliciesPage;
