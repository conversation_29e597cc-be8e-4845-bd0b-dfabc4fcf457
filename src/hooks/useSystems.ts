import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { systemsApi, SystemsQueryParams } from '@/services/systems.api';
import { System, CreateSystemForm } from '@/types';

// Query Keys
export const SYSTEMS_QUERY_KEYS = {
  all: ['systems'] as const,
  lists: () => [...SYSTEMS_QUERY_KEYS.all, 'list'] as const,
  list: (params?: SystemsQueryParams) => [...SYSTEMS_QUERY_KEYS.lists(), params] as const,
  details: () => [...SYSTEMS_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...SYSTEMS_QUERY_KEYS.details(), id] as const,
  health: (id: string) => [...SYSTEMS_QUERY_KEYS.detail(id), 'health'] as const,
};

// Get systems list
export const useSystems = (params?: SystemsQueryParams) => {
  return useQuery({
    queryKey: SYSTEMS_QUERY_KEYS.list(params),
    queryFn: () => systemsApi.getSystems(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get system by ID
export const useSystem = (id: string) => {
  return useQuery({
    queryKey: SYSTEMS_QUERY_KEYS.detail(id),
    queryFn: () => systemsApi.getSystemById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

// Get system health
export const useSystemHealth = (id: string) => {
  return useQuery({
    queryKey: SYSTEMS_QUERY_KEYS.health(id),
    queryFn: () => systemsApi.getSystemHealth(id),
    enabled: !!id,
    refetchInterval: 30 * 1000, // Refetch every 30 seconds
  });
};

// Create system mutation
export const useCreateSystem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateSystemForm) => systemsApi.createSystem(data),
    onSuccess: () => {
      // Invalidate and refetch systems list
      queryClient.invalidateQueries({ queryKey: SYSTEMS_QUERY_KEYS.lists() });
    },
  });
};

// Update system mutation
export const useUpdateSystem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<CreateSystemForm> }) =>
      systemsApi.updateSystem(id, data),
    onSuccess: (updatedSystem: System) => {
      // Update the specific system in cache
      queryClient.setQueryData(
        SYSTEMS_QUERY_KEYS.detail(updatedSystem.id),
        updatedSystem
      );
      // Invalidate systems list to reflect changes
      queryClient.invalidateQueries({ queryKey: SYSTEMS_QUERY_KEYS.lists() });
    },
  });
};

// Delete system mutation
export const useDeleteSystem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => systemsApi.deleteSystem(id),
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: SYSTEMS_QUERY_KEYS.detail(deletedId) });
      // Invalidate systems list
      queryClient.invalidateQueries({ queryKey: SYSTEMS_QUERY_KEYS.lists() });
    },
  });
};
