import { FC } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider, CssBaseline } from '@mui/material';
import { osoTheme } from '@/styles/osoTheme';
import { ROUTES } from '@/constants';

// Components
import ErrorBoundary from '@/components/ErrorBoundary';

// Layout Components
import DashboardLayout from '@/layouts/DashboardLayout';

// Page Components
import DashboardPage from '@/pages/DashboardPage';
import ProductsPage from '@/pages/ProductsPage';
import PoliciesPage from '@/pages/PoliciesPage';
import DecisionLogPage from '@/pages/DecisionLogPage';
import SettingsPage from '@/pages/SettingsPage';

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchOnWindowFocus: false,
    },
  },
});

const App: FC = () => {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider theme={osoTheme}>
          <CssBaseline />
          <Router>
            <Routes>
              {/* Redirect root to dashboard */}
              <Route path="/" element={<Navigate to={ROUTES.DASHBOARD} replace />} />

              {/* Main application routes with dashboard layout */}
              <Route path="/" element={<DashboardLayout />}>
                <Route path={ROUTES.DASHBOARD} element={<DashboardPage />} />
                <Route path={ROUTES.PRODUCTS} element={<ProductsPage />} />
                <Route path={ROUTES.POLICIES} element={<PoliciesPage />} />
                <Route path={ROUTES.DECISIONS} element={<DecisionLogPage />} />
                <Route path={ROUTES.SETTINGS} element={<SettingsPage />} />
              </Route>

              {/* Catch all route - redirect to dashboard */}
              <Route path="*" element={<Navigate to={ROUTES.DASHBOARD} replace />} />
            </Routes>
          </Router>
        </ThemeProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;
